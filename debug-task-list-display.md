# 任务列表显示问题诊断

## 问题描述
任务列表页面拉到了数据，但是没有展示出来。

## 可能的原因和排查步骤

### 1. 数据结构问题
**检查点**：API返回的数据结构是否正确
```typescript
// 期望的数据结构
{
  data: {
    rows: Array<CreateZoneTaskReq>,
    total: number,
    limit: number,
    offset: number,
    keyword: string
  }
}
```

**排查方法**：
1. 打开浏览器开发者工具
2. 查看 Console 中的日志输出
3. 检查以下信息：
   - `API 返回数据:` - 完整的响应数据
   - `res.data:` - 响应的 data 字段
   - `res.data.rows:` - 任务列表数组
   - `taskList.value:` - 最终赋值给表格的数据

### 2. 数据为空的情况
**可能原因**：
- 后端返回空数组
- 数据库中确实没有数据
- 权限问题导致无法获取数据

**排查方法**：
- 检查 `taskList.value` 的长度
- 如果为空，会显示"暂无任务数据"提示

### 3. 表格渲染问题
**可能原因**：
- 数据字段名不匹配
- 格式化函数出错
- Vue响应式数据问题

**排查方法**：
- 检查表格列的 `prop` 属性是否与数据字段匹配
- 检查格式化函数是否正常工作

### 4. 网络请求问题
**可能原因**：
- API请求失败
- 网络错误
- 后端服务异常

**排查方法**：
- 查看 Network 标签页中的请求状态
- 检查是否有错误日志输出

## 修复内容

### 1. 添加了详细的调试日志
```typescript
const loadTaskList = async () => {
  loading.value = true;
  try {
    const res = await HighlyAvailableAPI.listZoneTasksUsingGET();
    console.log('API 返回数据:', res);
    console.log('res.data:', res.data);
    console.log('res.data.rows:', res.data?.rows);
    taskList.value = res.data?.rows || [];
    console.log('taskList.value:', taskList.value);
  } catch (error) {
    console.error('获取任务列表失败:', error);
  } finally {
    loading.value = false;
  }
};
```

### 2. 改进了表格显示
- 添加了边框和斑马纹样式
- 设置了合适的列宽
- 添加了空状态提示
- 固定了操作列

### 3. 添加了错误处理
- 捕获并显示网络请求错误
- 提供友好的错误提示

## 排查步骤

1. **打开页面**，查看浏览器控制台
2. **检查日志输出**：
   ```
   API 返回数据: {...}
   res.data: {...}
   res.data.rows: [...]
   taskList.value: [...]
   ```
3. **分析数据**：
   - 如果 `res.data` 为 null/undefined，说明API响应格式有问题
   - 如果 `res.data.rows` 为空数组，说明后端没有数据
   - 如果 `taskList.value` 有数据但表格不显示，说明是渲染问题

4. **检查网络请求**：
   - 打开 Network 标签页
   - 查看 `/api/management/ha/zone-task` 请求
   - 检查响应状态码和响应内容

5. **检查数据格式**：
   - 确认返回的数据字段名是否正确
   - 检查是否有必需的字段缺失

## 常见问题解决方案

### 问题1：数据有值但表格不显示
**解决方案**：检查数据字段名是否与表格列的 `prop` 匹配

### 问题2：格式化函数报错
**解决方案**：检查 `regionOptions`、`envOptions`、`zoneOptions` 是否正确导入

### 问题3：权限问题
**解决方案**：检查用户是否有访问该API的权限

### 问题4：后端服务异常
**解决方案**：联系后端开发人员检查服务状态

## 下一步操作

1. 运行页面并查看控制台输出
2. 根据日志信息确定具体问题
3. 如果数据正常但不显示，检查Vue组件渲染逻辑
4. 如果API请求失败，检查网络和后端服务
