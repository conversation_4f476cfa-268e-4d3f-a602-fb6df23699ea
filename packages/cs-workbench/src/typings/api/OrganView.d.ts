declare namespace  AbcAPI {
    
    /**
     * 门店视图
     */
    type OrganView = {    
        //市id
        addressCityId:string    
        //门店地址:市
        addressCityName:string    
        //门店地址:详细地址
        addressDetail:string    
        //区id
        addressDistrictId:string    
        //门店地址:区
        addressDistrictName:string    
        //省id
        addressProvinceId:string    
        //门店地址:省
        addressProvinceName:string    
        //经营方式: 0:未知 1:直营 2:加盟
        busMode:number    
        //诊所类型
        category:string    
        //创建时间
        createdDate:string    
        //版本
        editionId:string    
        
        expiredTime:string    
        //1诊所管家；2口腔管家；3眼科管家
        hisType:number    
        //门店id
        id:string    
        //门店名
        name:string    
        //节点类型：1：总部；2：子店
        nodeType:number    
        //连锁id
        parentId:string    
        //连锁短id
        parentShortId:string    
        //门店短id
        shortId:string    
        //门店短名
        shortName:string    
        //视图：0：普通视图；1：单店视图
        viewMode:number    
    }
}
