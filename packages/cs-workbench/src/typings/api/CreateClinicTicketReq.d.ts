declare namespace  AbcAPI {
    
    type CreateClinicTicketReq = {    
        //连锁id
        chainId?:string    
        //补充信息
        chatInputValue?:Array<ChatInputValueDto>    
        //外部联系人ID
        clientId?:string    
        //门店id
        clinicId?:string    
        //会话ID
        conversationId?:string    
        //处理人名字
        dealerName?:string    
        //反馈人id
        employeeId?:string    
        //反馈时间 yyyy-MM-dd HH:mm:ss
        feedbackTime?:string    
        //跟进人id
        followerId?:string    
        //{@linkClinicTicket.FromWay}来源-0：1v1客服，1：外部群，2：客户于系统提单，3：内部人员提单，90其他
        fromWay?:number    
        //产品
        hisType?:number    
        //是否是草稿
        isDraft?:number    
        //是否紧急
        isEmergency?:number    
        //企微聊天记录
        msgList?:Array<QwConversationMsg>    
        //备注
        remark?:string    
        //群聊名称
        roomName?:string    
        //客服ID
        servicerId?:string    
        //标签
        tagList?:Array<any>    
        //问题分类名称列表
        tagNameList?:Array<any>    
        //tapd 处理人的id
        tapdDealerId?:string    
        //tapd id
        tapdId?:string    
        //tapd单链接
        tapdLick?:string    
        //标题
        title?:string    
        //工单类型 0bug 1需求 2实施 4支持(已废弃) 5商务 6专网
        type?:number    
    }
}
