import { computed, ComputedRef, ref } from 'vue';
import { CrmClientApi } from '@/api/crm-client-api.ts';
import { ElMessage } from 'element-plus';
import { createCache } from '@abc-oa/utils/src/cache.ts';
import { createDateTimeFormat19 } from '@abc-oa/utils/src/utils.ts';
import { formatHisTypeName } from '@abc-oa/utils/src/format.ts';
import { formatEdition } from '@abc-oa/common';
import { ClinicModuleApi } from '@/api/clinic-module-api.ts';

const clinicCache = createCache({
    max: 20,
});
interface ClinicInfoService {
    clinicInfo: any;
    isLoading: any;
    clinicInfoList: ComputedRef<{ label: string; value: any }[]>;
    printConfig: ComputedRef<any>;
    fetchPrintConfig: (chainId: string, clinicId: string) => Promise<void>;
    fetchClinicInfo: (chainId: string, clinicId: string, employeeId?: string) => Promise<void>;
    destroy: () => void;
}

let instance: ClinicInfoService | null;
export const useClinicInfoService = () => {
    if (instance) {
        return instance;
    }
    const clinicInfo = ref({});
    const isLoading = ref(false);

    const clinicInfoList = computed(() => [
        { label: '门店名称', value: clinicInfo.value.clinicName },
        { label: '门店ID', value: clinicInfo.value.clinicId, canCopy: true },
        { label: '门店短ID', value: clinicInfo.value.clinicShortId, canCopy: true },
        { label: '门店类型', value: clinicInfo.value.nodeTypeName },
        { label: '门店分区', value: clinicInfo.value.regionId },
        { label: '运行环境', value: clinicInfo.value.organEnv },
        { label: '总部名称', value: clinicInfo.value.chainName },
        { label: '总部ID', value: clinicInfo.value.chainId, canCopy: true },
        { label: '总部短ID', value: clinicInfo.value.chainShortId, canCopy: true },
        { label: '管理员ID', value: clinicInfo.value.employeeId, canCopy: true },
        { label: '管理员姓名', value: clinicInfo.value.employeeName },
        { label: '管理员电话', value: clinicInfo.value.employeeMobile, canCopy: true, quickLogin: true },
        { label: '管理员openId', value: clinicInfo.value.employeeOpenId, canCopy: true, quickLogin: true },
        { label: '产品类型', value: formatHisTypeName(clinicInfo.value.hisType, false) || '-' },
        { label: '版本', value: formatEdition(clinicInfo.value.editionId, false) || '-' },
        { label: '生效时间', value: createDateTimeFormat19(clinicInfo.value.editionBeginDate) || '-' },
        { label: '到期时间', value: createDateTimeFormat19(clinicInfo.value.editionEndDate) || '-' },
        { label: '公众号AppId', value: clinicInfo.value.mpAppId || '-', canCopy: true },
        { label: '小程序AppId', value: clinicInfo.value.weAppId || '-', canCopy: true },
        { label: '客户经理', value: clinicInfo.value.sellerName || '-' },
        { label: '企业微信ID', value: clinicInfo.value.qwCorpId || '-', canCopy: true },
        { label: '企业微信名称', value: clinicInfo.value.qwCorpName || '-' },
        { label: '总员工数', value: clinicInfo.value.employeeCount },
        { label: '是否试用门店', value: clinicInfo.value.isTrial === 1 ? '是' : '否' },
        { label: '打印引擎', value: printConfig.value.prescriptionVersion || printConfig.value.isEnableDesktopPrint ? '客户端打印' : 'Lodop打印' },
    ]);

    async function fetchClinicInfo(chainId: string, clinicId: string, employeeId?: string) {
        isLoading.value = true;
        try {
            // 优先从缓存取
            const cacheKey = `${clinicId}-${employeeId}`;
            const cacheData = clinicCache.get(cacheKey);
            if (cacheData) {
                clinicInfo.value = cacheData;
                fetchPrintConfig(chainId, clinicId);
                return;
            }

            const [res] = await Promise.all([
                CrmClientApi.getApiLowCodeCrmClinicInfoByClinicId(chainId, 1, clinicId),
                fetchPrintConfig(chainId, clinicId),
            ]);
            clinicCache.set(cacheKey, res);
            clinicInfo.value = res;
        } catch (e) {
            ElMessage.error('获取诊所信息失败', e);
        } finally {
            isLoading.value = false;
        }
    }

    const printConfig = ref({
        isEnableDesktopPrint: 0,
        prescriptionVersion: 0,
    });
    /**
     * 获取打印配置
     */
    async function fetchPrintConfig(chainId: string, clinicId: string) {
        let res: any = {};
        try {
            res = await ClinicModuleApi.getApiLowCodeClinicModulePrintConfig(clinicId, chainId);
        } catch (e: any) {
            ElMessage.error(e.message || e);
        }
        if (res.code === 200) {
            const { isEnableDesktopPrint, prescriptionVersion } = res.data;
            printConfig.value = {
                isEnableDesktopPrint,
                prescriptionVersion,
            };
        }
    }

    function destroy() {
        instance = null;
    }

    instance = {
        clinicInfo,
        isLoading,
        clinicInfoList,
        fetchClinicInfo,
        fetchPrintConfig,
        destroy,
    };
    return instance;
};
