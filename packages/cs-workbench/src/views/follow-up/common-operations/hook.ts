import { ref, reactive, computed } from 'vue';
import { OperationEnum, operationOptions, OperationItem, OperationHistoryItem, FormConfig, OperationSubTypeEnum, FormItem, FormDataType } from './model';
import { validateMobilePhone, formatDate } from '@abc-oa/utils';
import { JenkinsAPI } from '@/api/jenkins-api.ts';
import { ElMessage } from 'element-plus';
import { ClinicAPI } from '@/api/clinic-api';
import { useClinicInfoService } from '@/views/follow-up/hook/clinic-info-service.ts';
import _ from 'lodash';
import dayjs from 'dayjs';

// 创建中心 hook，管理所有共享状态
export const useCommonOperationState = () => {
    const { clinicInfo } = useClinicInfoService() as any;

    const formData = reactive<Record<string, any>>({});
    // 统一管理核心参数，使用 computed 避免直接解构导致的响应性丢失
    const clinicId = computed(() => clinicInfo.value?.clinicId);
    const chainId = computed(() => clinicInfo.value?.chainId);
    const clinicShortName = computed(() => clinicInfo.value?.clinicShortName);
    const clinicName = computed(() => clinicInfo.value?.clinicName);
    const chainName = computed(() => clinicInfo.value?.chainName);
    const regionId = computed(() => clinicInfo.value?.regionId);

    const NanJingCityCode = ['jiangsu_nanjing'];
    const SuZhouCityCode = ['jiangsu_suzhou'];
    const JinanCityCode = ['shandong_jinan'];
    const currentRegion = ref('');
    const isNanJing = computed(() => NanJingCityCode.includes(currentRegion.value));
    const isSuZhou = computed(() => SuZhouCityCode.includes(currentRegion.value));
    const isJinan = computed(() => JinanCityCode.includes(currentRegion.value));

    // 确认弹窗
    const confirmDialogVisible = ref(false);

    // 需要修改手机号的门店ID列表
    const modifyPhoneClinicIds = ref<any[]>([]);
    // 医保实收的费用列表
    const receivedFeeList = ref<any[]>([]);
    // 历史收费单列表
    const chargeSheetList = ref<any[]>([]);
    // 患者姓名搜索结果
    const patientNameList = ref<any[]>([]);

    const switchOptions = computed(() => {
        if (formData.propertyKey === 'clinicBasic.stat.viewMode') {
            return [
                { label: '开', value: 0 },
                { label: '关', value: 10 },
            ];
        }
        return [
            { label: '开', value: 1 },
            { label: '关', value: 0 },
        ];
    });

    // 获取社保配置
    const getShebaoConfigs = async (id?: string) => {
        let res: any = {};
        if (!clinicId.value && !id) return res;
        try {
            res = await ClinicAPI.getShebaoConfigsUsingGET(id || clinicId.value);
        } catch (e: any) {
            ElMessage.error(e.message || e);
        }
        currentRegion.value = res?.mainRegion;
        return res;
    };

    // 获取诊所用户列表
    const getClinicUserList = async (isFilter = 0) => {
        let res: any = {};
        if (!clinicId.value) return res;
        try {
            res = await ClinicAPI.getEmployeesUsingGET(clinicId.value, isFilter);
        } catch (e: any) {
            ElMessage.error(e.message || e);
        }
        return res?.rows || [];
    };
    const departmentsOptions = ref<any[]>([]);

    const getDepartments = async () => {
        let res: any = {};
        if (!clinicId.value) return res;
        try {
            res = await ClinicAPI.getDepartmentsUsingGET(clinicId.value);
        } catch (e: any) {
            ElMessage.error(e.message || e);
        }
        departmentsOptions.value = res.rows || [];
        return res?.rows || [];
    };

    const fetchPatientNames = async (keyword: string) => {
        console.log('fetchPatientNames', keyword);
        if (!keyword) return [];
        let res: any = {};
        try {
            res = await ClinicAPI.searchPatientBasicInfoUsingGET(commonOperationState.chainId.value,
                keyword,
                100,
                0);
        } catch (e: any) {
            console.error(e.message || e);
        }
        console.log('fetchPatientNames result', res);
        patientNameList.value = res?.list?.map((item: any) => ({
            label: `${item.name || ''}(${item.mobile || ''})`,
            value: item.id,
            ...item,
        })) || [];
    };
    const _debounceFetchPatientNames = _.debounce(fetchPatientNames, 800);

    const twoYearsAgo = new Date();
    twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
    const chargeSheetQuery = ref({
        patientId: '',
        beginDate: dayjs(twoYearsAgo).startOf('day').toDate() as unknown as string,
        endDate: dayjs(twoYearsAgo).endOf('day').toDate() as unknown as string,
    });
    const fetchChargeSheetList = async () => {
        if (!chargeSheetQuery.value.patientId && !chargeSheetQuery.value.beginDate && !chargeSheetQuery.value.endDate) {
            return;
        }
        let res: any = {};
        console.log('fetchChargeSheetList', chargeSheetQuery.value, chargeSheetQuery.value.beginDate, chargeSheetQuery.value.endDate);
        try {
            res = await ClinicAPI.queryPatientChargeSheetsUsingPOST({
                clinicId: clinicId.value,
                chainId: chainId.value,
                patientId: chargeSheetQuery.value.patientId,
                // 转成字符串格式
                beginDate: chargeSheetQuery.value.beginDate,
                endDate: chargeSheetQuery.value.endDate,
            });
        } catch (e: any) {
            console.error(e.message || e);
        }

        commonOperationState.chargeSheetList.value = res?.rows?.map((item: any) => ({
            ...item,
            label: `${item.receivableFee}(于${formatDate(item.created)}完成收费)`,
            value: item,
        })) || [];
    };
    return {
        formData,
        clinicId,
        chainId,
        clinicShortName,
        clinicName,
        regionId,
        chainName,
        currentRegion,
        isNanJing,
        isSuZhou,
        isJinan,
        confirmDialogVisible,
        modifyPhoneClinicIds,
        receivedFeeList,
        chargeSheetList,
        departmentsOptions,
        patientNameList,
        switchOptions,
        chargeSheetQuery,
        getDepartments,
        getShebaoConfigs,
        getClinicUserList,
        _debounceFetchPatientNames,
        fetchChargeSheetList,
    };
};

// 导出中心 hook 实例，以便全局使用
export const commonOperationState = useCommonOperationState();

/**
 * 获取社保补录码表单项配置
 * @returns 表单项配置数组
 */
const getSocialCodeFormItems = (): FormItem[] => {
    // 基础表单项 - 所有类型都需要的
    const baseItems: FormItem[] = [
        {
            type: 'radio',
            label: '补录类型',
            prop: 'subType',
            required: true,
            options: [
                { label: '西药/中成药', value: OperationSubTypeEnum.MEDICINE },
                { label: '医用耗材', value: OperationSubTypeEnum.MATERIAL },
                { label: '诊疗项目', value: OperationSubTypeEnum.TREATMENT },
                { label: '中药颗粒/饮片', value: OperationSubTypeEnum.TCM },
            ],
            defaultValue: OperationSubTypeEnum.MEDICINE,
            onChange: (value: OperationSubTypeEnum, formData: any, formRef: any) => {
                formRef?.clearValidate();
            },
        },
        {
            type: 'input',
            label: '社保码',
            prop: 'shebao_code',
            placeholder: '请输入社保码',
            required: true,
        },
    ];

    // 中心码表单项
    const centerCodeItem: FormItem = {
        type: 'input',
        label: '中心码',
        prop: 'cent_code',
        placeholder: () => (commonOperationState.isSuZhou.value ? '请输入省码' : '请输入中心码'),
        visible: (formData) => {
            const { subType } = formData;
            if (subType === OperationSubTypeEnum.MEDICINE || subType === OperationSubTypeEnum.TCM) {
                // 西药/中成药、中药颗粒/饮片：仅南京可见
                return commonOperationState.isNanJing.value;
            }
            if (subType === OperationSubTypeEnum.MATERIAL) {
                // 医用耗材：南京或苏州可见
                return commonOperationState.isNanJing.value || commonOperationState.isSuZhou.value;
            }
            if (subType === OperationSubTypeEnum.TREATMENT) {
                // 诊疗项目：仅南京可见
                return commonOperationState.isNanJing.value;
            }
            return false;
        },
        required: (formData) => {
            // 如果可见则必填
            if (typeof centerCodeItem.visible === 'function') {
                return centerCodeItem.visible(formData);
            }
            return false;
        },
    };

    // 市码表单项 - 仅苏州医用耗材时显示
    const cityCodeItem: FormItem = {
        type: 'input',
        label: '市码',
        prop: 'city_code',
        placeholder: '请输入市码',
        visible: (formData) => commonOperationState.isSuZhou.value && formData.subType === OperationSubTypeEnum.MATERIAL,
        required: (formData) => commonOperationState.isSuZhou.value && formData.subType === OperationSubTypeEnum.MATERIAL,
    };

    // 名称表单项 - 医用耗材必填，诊疗项目可选
    const nameItem: FormItem = {
        type: 'input',
        label: '名称',
        prop: 'name',
        placeholder: '请输入名称',
        visible: (formData) => formData.subType === OperationSubTypeEnum.MATERIAL || formData.subType === OperationSubTypeEnum.TREATMENT,
        required: (formData) => formData.subType === OperationSubTypeEnum.MATERIAL || formData.subType === OperationSubTypeEnum.TREATMENT,
    };

    // 医用耗材特有表单项
    const materialItems: FormItem[] = [
        {
            type: 'input',
            label: '生产厂商',
            prop: 'manufacture',
            placeholder: '请输入生产厂商',
            visible: (formData) => formData.subType === OperationSubTypeEnum.MATERIAL,
        },
        {
            type: 'input',
            label: '注册证号',
            prop: 'approval_code',
            placeholder: '请输入注册证号',
            visible: (formData) => formData.subType === OperationSubTypeEnum.MATERIAL,
        },
        {
            type: 'input',
            label: '规格',
            prop: 'spec',
            placeholder: '请输入规格',
            visible: (formData) => formData.subType === OperationSubTypeEnum.MATERIAL,
        },
        {
            type: 'input',
            label: '型号',
            prop: 'model',
            placeholder: '请输入型号',
            visible: (formData) => formData.subType === OperationSubTypeEnum.MATERIAL,
        },
    ];

    // 诊疗项目特有表单项
    const treatmentItems: FormItem[] = [
        {
            type: 'input',
            label: '单位',
            prop: 'unit',
            placeholder: '请输入单位',
            visible: (formData) => formData.subType === OperationSubTypeEnum.TREATMENT,
        },
        {
            type: 'input',
            label: '包括',
            prop: 'include',
            placeholder: '请输入包括内容',
            visible: (formData) => formData.subType === OperationSubTypeEnum.TREATMENT,
        },
        {
            type: 'input',
            label: '备注',
            prop: 'remark',
            placeholder: '请输入备注',
            visible: (formData) => formData.subType === OperationSubTypeEnum.TREATMENT,
        },
    ];

    // 合并所有表单项
    return [
        ...baseItems,
        centerCodeItem,
        cityCodeItem,
        nameItem,
        ...materialItems,
        ...treatmentItems,
    ];
};

// 表单配置映射
const formConfigMap = (): Record<OperationEnum, FormConfig> => ({
    [OperationEnum.SOCIAL_CODE]: {
        title: '补录社保码',
        items: getSocialCodeFormItems(),
        rules: {
            subType: [{ required: true, message: '请选择补录类型', trigger: 'change' }],
            shebao_code: [{ required: true, message: '请输入社保码', trigger: 'blur' }],
            cent_code: [{
                required: (formData: FormDataType) => {
                    const { type } = formData;
                    if (type === OperationSubTypeEnum.MEDICINE || type === OperationSubTypeEnum.TCM) {
                        return commonOperationState.isNanJing.value;
                    }
                    if (type === OperationSubTypeEnum.MATERIAL) {
                        return commonOperationState.isNanJing.value || commonOperationState.isSuZhou.value;
                    }
                    if (type === OperationSubTypeEnum.TREATMENT) {
                        return commonOperationState.isNanJing.value;
                    }
                    return false;
                },
                message: commonOperationState.isSuZhou.value ? '请输入省码' : '请输入中心码',
                trigger: 'blur',
            }],
            city_code: [{
                required: (formData) => commonOperationState.isSuZhou.value && formData.type === OperationSubTypeEnum.MATERIAL,
                message: '请输入市码',
                trigger: 'blur',
            }],
            name: [{
                required: (formData) => formData.type === OperationSubTypeEnum.MATERIAL,
                message: '请输入名称',
                trigger: 'blur',
            }],
        },
    },
    [OperationEnum.MODIFY_VERSION]: {
        title: '修改版本时长',
        items: [
            {
                type: 'datePicker',
                label: '到期日期',
                prop: 'endDate',
                dateType: 'date',
                placeholder: '选择到期日期',
                format: 'YYYY-MM-DD',
                // 默认值为明天
                defaultValue: () => {
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    const result = formatDate(tomorrow, 'YYYY-MM-DD');
                    return result;
                },
                // 日期选择器的快捷选项
                pickerOptions: {
                    shortcuts: [
                        {
                            text: '明天',
                            value: () => {
                                const date = new Date();
                                date.setDate(date.getDate() + 1);
                                return date;
                            },
                        },
                        {
                            text: '后天',
                            value: () => {
                                const date = new Date();
                                date.setDate(date.getDate() + 2);
                                return date;
                            },
                        },
                        {
                            text: '一周后',
                            value: () => {
                                const date = new Date();
                                date.setDate(date.getDate() + 7);
                                return date;
                            },
                        },
                    ],
                    disabledDate: (time: Date) => {
                        // 禁用今天之前的日期
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        if (time.getTime() < today.getTime()) {
                            return true;
                        }

                        // 禁用1个月后的日期
                        const maxDate = new Date();
                        maxDate.setDate(maxDate.getDate() + 30);
                        maxDate.setHours(23, 59, 59, 999);
                        return time.getTime() > maxDate.getTime();
                    },
                },
            },
            {
                type: 'input',
                label: '修改原因',
                prop: 'reason',
                placeholder: '请输入修改原因',
            },
        ],
        rules: {
            expireDate: [{ required: true, message: '请选择到期日期', trigger: 'change' }],
            reason: [{ required: true, message: '请输入修改原因', trigger: 'blur' }],
        },
    },
    [OperationEnum.CLEAR_REDIS]: {
        title: '清理GoodsRedis缓存',
        items: [
            {
                type: 'radio',
                label: '清理类型',
                prop: 'opType',
                required: true,
                options: [
                    { label: '只清理缓存', value: '只清理缓存' },
                    { label: '刷药品项目同步社保Stat&清理缓存', value: '刷药品项目同步社保Stat&清理缓存' },
                    { label: '前端搜索(解决搜索问题)', value: '前端搜索(解决搜索问题)' },
                    { label: '刷药品项目同步社保&前端搜索&清理缓存', value: '刷药品项目同步社保&前端搜索&清理缓存' },
                ],
            },
            {
                type: 'tips',
                label: '',
                alertType: 'error',
                prop: 'warning',
                content: '注意： 服务器缓存重建期间，不要频繁点击重建dump.json!',
                style: { color: 'red', fontSize: '12px', marginTop: '10px' },
            },
            {
                type: 'input',
                label: '连锁ID',
                prop: 'chainId',
                disabled: true,
                visible: () => false,
                defaultValue: commonOperationState.chainId.value || '',
            },
        ],
        rules: {
            opType: [{ required: true, message: '请选择清理类型', trigger: 'change' }],
        },
    },
    [OperationEnum.SWITCH_SETTING]: {
        title: '开关设置',
        items: [
            {
                type: 'select',
                label: '开关类型',
                prop: 'propertyKey',
                required: true,
                placeholder: '请选择开关类型',
                options: [
                    { label: '客户端打印 (chainBasic.isEnableDesktopPrint)', value: 'chainBasic.isEnableDesktopPrint' },
                    { label: '新版处方 (clinicBasic.printConfig.prescriptionVersion)', value: 'clinicBasic.printConfig.prescriptionVersion' },
                    { label: '新版药店收费小票 (clinicBasic.printConfig.pharmacyCashierVersion)', value: 'clinicBasic.printConfig.pharmacyCashierVersion' },
                    { label: '门诊日志导出/收费明细查看限制 (clinicBasic.stat.viewMode)', value: 'clinicBasic.stat.viewMode' },
                ],
                onChange: (value: any, formData: any) => {
                    // 根据propertyKey前缀设置scopeId
                    if (value?.startsWith('chainBasic')) {
                        formData.scopeId = commonOperationState.chainId.value || '';
                    } else if (value?.startsWith('clinicBasic')) {
                        formData.scopeId = commonOperationState.clinicId.value || '';
                    }
                    
                    // 当选择门诊日志导出限制时，默认选中"关"（解除限制）
                    if (value === 'clinicBasic.stat.viewMode') {
                        formData.value = 10; // 默认选中"关"，对应jenkins传值10
                    } else {
                        // 其他选项清空默认值，让用户自己选择
                        formData.value = undefined;
                    }
                },
            },
            {
                type: 'radio',
                label: '开关操作',
                prop: 'value',
                required: true,
                options: commonOperationState.switchOptions.value,
            },
            {
                type: 'input',
                label: '连锁名称',
                prop: 'scopeId',
                disabled: true,
                visible: () => false,
                defaultValue: (formData: any) => {
                    // 根据propertyKey前缀决定使用chainId还是clinicId
                    if (formData?.propertyKey?.startsWith('chainBasic')) {
                        return commonOperationState.chainId.value || '';
                    }
                    if (formData?.propertyKey?.startsWith('clinicBasic')) {
                        return commonOperationState.clinicId.value || '';
                    }
                    return commonOperationState.chainId.value || '';
                },
            },
            {
                type: 'input',
                label: '门店分区',
                prop: 'regionId',
                disabled: true,
                visible: () => false,
                defaultValue: commonOperationState.regionId.value?.replace('region', '') || '',
            },
        ],
        rules: {
            propertyKey: [{ required: true, message: '请选择开关类型', trigger: 'change' }],
            switchValue: [{ required: true, message: '请选择开关操作', trigger: 'change' }],
        },
    },
    [OperationEnum.ADJUST_CHARGE]: {
        title: '调整医保收费单为异常状态',
        items: [
            {
                type: 'input',
                label: '患者姓名',
                prop: 'patientName',
                required: true,
                placeholder: '请输入患者姓名',
                onChange: async (value: any, formData: any, formRef: any) => {
                    let res: any = {};
                    formData.receivedFee = undefined;
                    try {
                        res = await ClinicAPI.searchPatientShebaoOrderUsingPOST({
                            clinicId: commonOperationState.clinicId.value,
                            chainId: commonOperationState.chainId.value,
                            keyword: value,
                        });
                    } catch (e: any) {
                        console.error(e.message || e);
                    }

                    commonOperationState.receivedFeeList.value = res?.rows?.map((item: any) => ({
                        ...item,
                        label: `${item.receivedFee}(于${formatDate(item.chargedTime)}完成收费)`,
                        value: item,
                    })) || [];

                    formRef?.validateField('patientName');
                },
            },
            {
                type: 'selectV2',
                label: '医保实收',
                prop: 'receivedFee',
                required: true,
                valueKey: 'chargeSheetId',
                placeholder: '请选择收费单',
                options: commonOperationState.receivedFeeList.value,
                onChange: (value: any, formData: any) => {
                    const selectedOption = commonOperationState.receivedFeeList.value.find((item: any) => item.chargeSheetId === value);
                    if (selectedOption) {
                        formData.receivedFee = selectedOption.receivedFee;
                        formData.chargeSheetId = selectedOption.chargeSheetId;
                        formData.chargedTime = selectedOption.chargedTime ? formatDate(selectedOption.chargedTime) : '';
                    } else {
                        formData.receivedFee = undefined;
                        formData.chargeSheetId = undefined;
                        formData.chargedTime = '';
                    }
                },
            },
            {
                type: 'input',
                label: '收费单ID',
                prop: 'chargeSheetId',
                disabled: true,
                visible: () => false,
            },
            {
                type: 'input',
                label: '收费时间',
                prop: 'chargedTime',
                disabled: true,
                visible: () => false,
            },
            {
                type: 'input',
                label: '连锁ID',
                prop: 'chainId',
                disabled: true,
                visible: () => false,
                defaultValue: commonOperationState.chainId.value || '',
            },
            {
                type: 'input',
                label: '诊所ID',
                prop: 'clinicId',
                disabled: true,
                visible: () => false,
                defaultValue: commonOperationState.clinicId.value || '',
            },
        ],
        rules: {
            patientName: [
                {
                    trigger: 'blur',
                    validator: (rule: any, value: any, callback: any) => {
                        console.log(rule, value);

                        if (!value) {
                            callback(new Error('请输入患者姓名'));
                        } else if (commonOperationState.receivedFeeList.value.length === 0) {
                            callback(new Error('找不到该患者的医保收费的相关信息，请重新输入'));
                        } else {
                            callback();
                        }
                    } }],
            receivedFee: [{ required: true, message: '请选择收费单', trigger: 'change' }],
        },
        // 特殊处理标记，用于指示该操作需要二次确认
        needConfirm: true,
        // 确认对话框配置
        confirmConfig: {
            title: '确认调整如下医保收费单为异常状态吗？',
            content: [
                { label: '收费单ID', key: 'chargeSheetId' },
                { label: '患者姓名', key: 'patientName' },
                { label: '医保实收', key: 'receivedFee' },
                { label: '收费时间', key: 'chargedTime' },
            ],
            warning: '注意： 请认真核对以上收费单信息是否与客户的反馈一致！',
        },
    },
    [OperationEnum.TREATMENT_UPGRADE]: {
        title: '理疗预约升级开通',
        items: [
            {
                type: 'input',
                label: '连锁名称',
                prop: 'chainName',
                disabled: true,
                defaultValue: commonOperationState.chainName.value || '',
            },
            {
                type: 'select',
                label: '连锁名称',
                prop: 'chainIds',
                disabled: true,
                visible: () => false,
                defaultValue: [commonOperationState.chainId.value],
            },

        ],
        rules: {
            enableDate: [{ required: true, message: '请选择开通日期', trigger: 'change' }],
        },
    },
    [OperationEnum.MODIFY_DEPARTMENT]: {
        title: '修改科室编码',
        items: [
            {
                type: 'input',
                label: '诊所ID',
                prop: 'clinicId',
                disabled: true,
                visible: () => false,
                defaultValue: commonOperationState.clinicId.value || '',
            },
            {
                type: 'select',
                label: '科室名称',
                prop: 'mainMedicalName',
                required: true,
                placeholder: '请选择科室',
                options: [],
                optionsFn: async () => (await commonOperationState.getDepartments())?.map((item: any) => ({
                    label: `${item.name || ''}(编码：${item.customId || ''})`,
                    value: item.id,
                })),
                onChange: async (value: any, formData: any) => {
                    const selected = commonOperationState.departmentsOptions.value?.find((item: any) => item.id === value);
                    if (selected) {
                        formData.oldCode = selected.customId;
                        formData.name = selected.name;
                    }
                },
            },
            {
                type: 'input',
                label: '新编码',
                prop: 'newCode',
                placeholder: '请输入新的科室编码',
                required: true,
            },
        ],
        rules: {
            name: [{ required: true, message: '请选择科室', trigger: 'change' }],
            newCode: [{ required: true, message: '请输入新的科室编码', trigger: 'blur' }],
        },
    },
    [OperationEnum.MODIFY_PHONE]: {
        title: '修改系统成员手机号',
        items: [
            {
                type: 'select',
                label: '系统成员',
                prop: 'employeeId',
                required: true,
                placeholder: '请选择系统成员',
                options: [],
                optionsFn: async (clinicId: string) => (await commonOperationState.getClinicUserList())?.map((item: any) => ({
                    label: `${item.name}(${item.mobile})`,
                    value: item.id,
                })),
                onChange: async (value: any, formData: any) => {
                    let res: any = {};

                    try {
                        res = await ClinicAPI.getEmployeeClinicsUsingGET(value, 1);
                    } catch (e: any) {
                        ElMessage.error(e.message || e);
                    }

                    if (res?.rows) {
                        // 修改 clinicIds 对应的options
                        const clinicOptions = res.rows.map((item: any) => ({
                            label: item.chainName,
                            value: item.chainId,
                        }));
                        commonOperationState.modifyPhoneClinicIds.value = clinicOptions;
                        formData.chainIds = res.rows.map((item: any) => item.chainId);
                    }
                },
            },
            {
                type: 'input',
                label: '新手机号',
                prop: 'newMobile',
                placeholder: '请输入新的手机号',
                required: true,
            },
            {
                type: 'select',
                label: '影响连锁',
                prop: 'chainIds',
                placeholder: '请选择影响的连锁',
                multiple: true,
                options: commonOperationState.modifyPhoneClinicIds.value,
            },
        ],
        rules: {
            employeeId: [{ required: true, message: '请选择系统成员', trigger: 'change' }],
            newMobile: [
                {
                    required: true,
                    message: '请输入新的手机号',
                    trigger: 'blur',
                },
                {
                    validator: (rule, value, callback) => {
                        if (!value || validateMobilePhone(value)) {
                            callback();
                        } else {
                            callback(new Error('请输入正确的手机号格式'));
                        }
                    },
                    trigger: 'blur',
                },
            ],
            chainIds: [{ required: true, message: '请选择影响的连锁', trigger: 'change' }],
        },
    },
    [OperationEnum.SYNC_DRUG_INFO_GB]: {
        title: '同步国标药品信息',
        items: [
            {
                type: 'input',
                label: '门店',
                prop: 'clinicName',
                visible: () => false,
                disabled: true,
                defaultValue: commonOperationState.clinicName.value || '',
            },
        ],
        rules: {},
        needConfirm: true,
        // 确认对话框配置
        confirmConfig: {
            title: '确认同步国标药品资料吗？',
            content: [
                { label: '同步门店', key: 'clinicName' },
            ],
        },
    },
    [OperationEnum.SYNC_DRUG_INFO_JINAN]: {
        title: '同步济南药品信息',
        items: [
            {
                type: 'input',
                label: '门店',
                prop: 'clinicName',
                visible: () => false,
                disabled: true,
                defaultValue: commonOperationState.clinicName.value || '',
            },
        ],
        rules: {},
        needConfirm: true,
        // 确认对话框配置
        confirmConfig: {
            title: '确认同步济南药品资料吗？',
            content: [
                { label: '同步门店', key: 'clinicName' },
            ],
        },
    },
    [OperationEnum.SET_ADMIN]: {
        title: '设置管理员',
        items: [
            {
                type: 'select',
                label: '系统成员',
                prop: 'mobile',
                placeholder: '请选择系统成员',
                required: true,
                options: [],
                optionsFn: async (clinicId: string) => (await commonOperationState.getClinicUserList(1))?.map((item: any) => ({
                    label: `${item.name}(${item.mobile})`,
                    value: item.mobile,
                })),
            },
            {
                type: 'tips',
                label: '',
                alertType: 'error',
                prop: 'warning',
                content: '注意：此操作有风险，请在核实客户相关情况后再执行！',
                style: { color: 'red', fontSize: '12px', marginTop: '10px' },
            },
        ],
        rules: {
            mobile: [{ required: true, message: '请选择系统成员', trigger: 'change' }],
        },
    },
    [OperationEnum.UPDATE_LOCATION]: {
        title: '更新微诊所定位',
        items: [
            {
                type: 'input',
                label: '经度',
                prop: 'longitude',
                placeholder: '请输入数值（73～136）',
                required: true,
                inputType: 'number',
            },
            {
                type: 'input',
                label: '纬度',
                prop: 'latitude',
                placeholder: '请输入数值（3～54）',
                required: true,
                inputType: 'number',
            },
        ],
        rules: {
            longitude: [
                { required: true, message: '请输入经度', trigger: 'blur' },
                {
                    validator: (rule, value, callback) => {
                        const num = Number(value);
                        if (Number.isNaN(num) || num < 73 || num > 136) {
                            callback(new Error('经度必须在73到136之间'));
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur',
                },
            ],
            latitude: [
                { required: true, message: '请输入纬度', trigger: 'blur' },
                {
                    validator: (rule, value, callback) => {
                        const num = Number(value);
                        if (Number.isNaN(num) || num < 3 || num > 54) {
                            callback(new Error('纬度必须在3到54之间'));
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur',
                },
            ],
        },
    },
    [OperationEnum.CLINIC_VERSION]: {
        title: '门店独立产品版本开通',
        items: [
            {
                type: 'select',
                label: '独立版本',
                prop: 'purchaseItemName',
                placeholder: '请选择要开通的独立产品版本',
                required: true,
                options: [
                    { label: '叫号', value: '叫号' },
                    { label: '营销-满减活动', value: '营销-满减活动' },
                    { label: '营销-折扣', value: '营销-折扣' },
                    { label: '营销-优惠券', value: '营销-优惠券' },
                    { label: '营销-消息推送', value: '营销-消息推送' },
                    { label: '收费-自助续方', value: '收费-自助续方' },
                    { label: '套餐', value: '套餐' },
                    { label: '业绩提成报表', value: '业绩提成报表' },
                    { label: '随访', value: '随访' },
                    { label: '自助服务机', value: '自助服务机' },
                    { label: '患者评价', value: '患者评价' },
                    { label: '供应商结算', value: '供应商结算' },
                    { label: '微诊所', value: '微诊所' },
                    { label: '微信支付', value: '微信支付' },
                    { label: '库存异价调拨', value: '库存异价调拨' },
                    { label: '慢病康复', value: '慢病康复' },
                    { label: '一般问诊单', value: '一般问诊单' },
                    { label: '家庭医生', value: '家庭医生' },
                    { label: '体检系统', value: '体检系统' },
                    { label: '取药叫号', value: '取药叫号' },
                    { label: '多病历-口腔', value: '多病历-口腔' },
                    { label: '多病历-眼科', value: '多病历-眼科' },
                    { label: '加工', value: '加工' },
                    { label: '检查叫号', value: '检查叫号' },
                    { label: '检查叫号小屏', value: '检查叫号小屏' },

                ],
            },
            {
                type: 'datePicker',
                label: '开始日期',
                prop: 'beginDate',
                dateType: 'date',
                required: true,
                format: 'YYYY-MM-DD',
                placeholder: '请选择开始日期',
                defaultValue: formatDate(new Date(), 'YYYY-MM-DD'), // 默认为今天
                onChange: (value, formData) => {
                    // 在开始日期被选择后自动设置结束日期
                    if (formData.beginDate && !formData.endDate) {
                        const endDate = new Date(formData.beginDate);
                        endDate.setFullYear(endDate.getFullYear() + 1);
                        formData.endDate = formatDate(endDate, 'YYYY-MM-DD');
                    }
                },
            },
            {
                type: 'datePicker',
                label: '结束日期',
                prop: 'endDate',
                dateType: 'date',
                required: true,
                format: 'YYYY-MM-DD',
                placeholder: '请选择结束日期',
                defaultValue: formatDate(new Date(new Date().setFullYear(new Date().getFullYear() + 1)), 'YYYY-MM-DD'), // 默认为开始日期+1年
            },
            {
                type: 'input',
                label: '付款金额',
                prop: 'receivableFee',
                placeholder: '请输入付款金额',
                required: true,
                inputType: 'number',
            },
            {
                type: 'input',
                label: '门店ID',
                prop: 'bindClinicId',
                visible: () => false,
                disabled: true,
                defaultValue: commonOperationState.clinicId.value || '',
            },
            {
                type: 'input',
                label: '门店名称',
                prop: 'clinicName',
                visible: () => false,
                disabled: true,
                defaultValue: commonOperationState.clinicName.value || '',
            },
        ],
        rules: {
            versionType: [{ required: true, message: '请选择独立版本', trigger: 'change' }],
            beginDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
            endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
            receivableFee: [{ required: true, message: '请输入付款金额', trigger: 'blur' }],
        },
        needConfirm: true,
        confirmConfig: {
            title: '确认门店独立产品版本开通信息',
            content: [
                { label: '独立版本', key: 'purchaseItemName' },
                { label: '门店名称', key: 'clinicName' },
                { label: '开始日期', key: 'beginDate' },
                { label: '结束日期', key: 'endDate' },
                { label: '付款金额', key: 'receivableFee' },
            ],
        },
    },
    [OperationEnum.CANCEL_AUTH]: {
        title: '取消微诊所授权',
        items: [
            {
                type: 'radio',
                label: '重新授权是否变更公众号',
                prop: 'isChangeMp',
                required: true,
                labelWidth: '14em',
                options: [
                    { label: '是', value: 1 },
                    { label: '否', value: 0 },
                ],
                defaultValue: 0,
            },
        ],
        rules: {
            isChangeMp: [{ required: true, message: '请选择是否变更公众号', trigger: 'change' }],
        },
    },
    [OperationEnum.SOCIAL_EXPORT]: {
        title: '通用社保结算数据导出',
        items: [
            {
                type: 'datePicker',
                label: '开始日期',
                prop: 'startDate',
                format: 'YYYY-MM-DD',
                required: true,
                placeholder: '请选择开始日期',
                // 默认为去年的首日，如：2024-01-01
                defaultValue: formatDate(new Date(new Date().getFullYear() - 1, 0, 1), 'YYYY-MM-DD'),
                onChange: (value: string, formData: any) => {
                    if (value) {
                        const endDate = new Date(value);
                        // 结束时间为开始时间年份的12月31日
                        endDate.setFullYear(endDate.getFullYear());
                        endDate.setMonth(11);
                        endDate.setDate(31);
                        formData.endDate = formatDate(endDate, 'YYYY-MM-DD');
                    }
                },
            },
            {
                type: 'datePicker',
                label: '结束日期',
                prop: 'endDate',
                format: 'YYYY-MM-DD',
                required: true,
                placeholder: '请选择结束日期',
                defaultValue: formatDate(new Date(new Date().getFullYear() - 1, 11, 31), 'YYYY-MM-DD'),
            },
            {
                type: 'input',
                label: '导出分区门店名称',
                prop: 'clinicName',
                visible: () => false,
                disabled: true,
                defaultValue: commonOperationState.clinicName.value || '',
            },
            {
                type: 'input',
                label: '导出分区门店ID',
                prop: 'clinicId',
                visible: () => false,
                disabled: true,
                defaultValue: commonOperationState.clinicId.value || '',
            },
            {
                type: 'input',
                label: '导出分区连锁ID',
                prop: 'chainId',
                visible: () => false,
                disabled: true,
                defaultValue: commonOperationState.chainId.value || '',
            },
        ],
        rules: {
            startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
            endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
        },
    },
    [OperationEnum.QUERY_USAGE_OF_TRACE_CODE]: {
        title: '查询追溯码使用情况',
        items: [
            {
                type: 'input',
                label: '导出分区门店ID',
                prop: 'clinicId',
                visible: () => false,
                disabled: true,
                defaultValue: commonOperationState.clinicId.value || '',
            },
            {
                type: 'input',
                label: '追溯码',
                prop: 'traceCode',
            },
        ],
        rules: {
            traceCode: [{ required: true, message: '请输入追溯码', trigger: 'blur' }],
        },
    },
    [OperationEnum.UNBIND_WECHAT_ACCOUNT]: {
        title: '解绑系统成员微信',
        items: [
            {
                type: 'select',
                label: '系统成员',
                prop: 'employeeId',
                required: true,
                placeholder: '请选择系统成员',
                options: [],
                optionsFn: async () => (await commonOperationState.getClinicUserList())?.map((item: any) => ({
                    label: `${item.name}(${item.mobile})`,
                    value: item.id,
                })),
            },
            {
                type: 'tips',
                prop: 'unbindWarning',
                content: '注意： 此操作有风险，请在核实客户相关情况后再执行！',
                alertType: 'error',
            },
        ],
        rules: {
            employeeId: [{ required: true, message: '请选择系统成员', trigger: 'change' }],
        },
    },
    [OperationEnum.UPDATE_TWO_YEAR_AGO_SHEET_CAN_OPERATE]: {
        title: '修改两年前的收费单可退',
        items: [
            {
                type: 'select',
                label: '患者姓名',
                prop: 'patientName',
                required: true,
                placeholder: '请输入患者姓名并搜索',
                filterable: true,
                remote: true,
                options: commonOperationState.patientNameList.value,
                remoteMethod: commonOperationState._debounceFetchPatientNames,
                onChange: async (value: any, formData: any, formRef: any) => {
                    formData.receivableFee = undefined;
                    formData.chargeSheetId = undefined;
                    commonOperationState.chargeSheetQuery.value.patientId = value;
                    const name = commonOperationState.patientNameList.value.find((item: any) => item.id === value)?.name || '';
                    formData.patientName = name;
                    commonOperationState.fetchChargeSheetList();

                    formRef?.validateField('patientName');
                },
            },
            {
                type: 'datePicker',
                label: '收费日期',
                prop: 'endDate',
                dateType: 'date',
                placeholder: '选择查询日期',
                format: 'YYYY-MM-DD',
                defaultValue: () => {
                    const twoYearsAgo = new Date();
                    twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
                    return formatDate(twoYearsAgo, 'YYYY-MM-DD');
                },
                pickerOptions: {
                    disabledDate: (time: Date) => {
                        //     禁用近两年的
                        const twoYearsAgo = new Date();
                        twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
                        return time.getTime() > twoYearsAgo.getTime(); // 禁用两年前的日期
                    },
                },
                onChange: (value: string) => {
                    if (value) {
                        commonOperationState.chargeSheetQuery.value.beginDate = dayjs(value).startOf('day').toDate() as unknown as string;
                        commonOperationState.chargeSheetQuery.value.endDate = dayjs(value).endOf('day').toDate() as unknown as string;
                        commonOperationState.fetchChargeSheetList();
                    }
                },
            },
            {
                type: 'selectV2',
                label: '收费金额',
                prop: 'receivableFee',
                required: true,
                valueKey: 'id',
                placeholder: '请选择收费单',
                options: commonOperationState.chargeSheetList.value,
                onChange: (value: any, formData: any) => {
                    const selected = commonOperationState.chargeSheetList.value.find((item: any) => item.id === value);
                    if (selected) {
                        formData.receivableFee = selected.receivableFee;
                        formData.chargeSheetId = selected.id;
                        formData.chargedTime = selected.created ? formatDate(selected.created) : '';
                    } else {
                        formData.receivableFee = undefined;
                        formData.chargeSheetId = undefined;
                        formData.chargedTime = '';
                    }
                },
            },
            {
                type: 'input',
                label: '门店分区',
                prop: 'regionId',
                disabled: true,
                visible: () => false,
                defaultValue: commonOperationState.regionId.value?.replace('region', '') || '',
            },
        ],
        rules: {
            patientName: [{ required: true, message: '请输入患者姓名', trigger: 'blur' }],
            receivableFee: [{ required: true, message: '请选择收费单号', trigger: 'blur' }],
        },
        // 特殊处理标记，用于指示该操作需要二次确认
        needConfirm: true,
        // 确认对话框配置
        confirmConfig: {
            title: '确认调整如下患者的收费单为可退状态吗？',
            content: [
                { label: '收费单ID', key: 'chargeSheetId' },
                { label: '患者姓名', key: 'patientName' },
                { label: '收费金额', key: 'receivableFee' },
                { label: '收费时间', key: 'chargedTime' },
            ],
            warning: '注意： 请认真核对以上收费单信息是否与客户的反馈一致！',
        },
    },
    [OperationEnum.EXPORT_STOCK]: {
        title: '库存导出',
        items: [
            {
                type: 'input',
                label: '门店名称',
                prop: 'clinicName',
                disabled: true,
                defaultValue: commonOperationState.clinicName.value || '',
                required: true,
            },
            {
                type: 'input',
                label: '诊所ID',
                prop: 'clinicId',
                disabled: true,
                visible: () => false,
                defaultValue: commonOperationState.clinicId.value || '',
            },
        ],
    },
});

// 获取操作列表
export const useOperationList = () => {
    // 当前选中的操作项
    const currentOperation = ref<OperationItem | null>(null);

    // 当前操作的表单配置
    const currentFormConfig = computed(() => {
        if (!currentOperation.value) return null;
        return formConfigMap()[currentOperation.value.key];
    });

    // 设置当前操作项
    const setCurrentOperation = (operation: OperationItem | null) => {
        currentOperation.value = operation;
    };

    const operationList = ref<OperationItem[]>([]);
    const loading = ref(false);
    const operationHistoryCache = ref<Record<string, OperationHistoryItem[]>>({});
    const historyLoading = ref<Record<string, boolean>>({});
    const runningTasks = ref<OperationHistoryItem[]>([]);

    // 获取正在执行中的任务
    const fetchRunningTasks = async () => {
        if (!commonOperationState.clinicId.value) return false;
        try {
            const res: any = await JenkinsAPI.getExecutingTaskUsingGET(commonOperationState.clinicId.value);
            // 如果正在执行中任务数量发生变化，则刷新列表
            if ((res?.rows?.length || 0) !== (runningTasks.value?.length || 0)) {
                fetchJenkinsTaskList();
            }
            if (res?.rows && Array.isArray(res.rows)) {
                runningTasks.value = res.rows;
                return res.rows.length > 0;
            }
            runningTasks.value = [];
            return false;
        } catch (e: any) {
            console.error('获取执行中任务失败:', e);
            ElMessage.error(e.message || e);
            return false;
        }
    };

    const fetchJenkinsTaskList = async () => {
        if (!commonOperationState.clinicId.value) return;
        let res: any = {};
        try {
            res = await JenkinsAPI.getTaskListUsingGET(commonOperationState.clinicId.value);
        } catch (e: any) {
            ElMessage.error(e.message || e);
        }

        if (res?.rows && Array.isArray(res.rows)) {
            // 创建type到OperationEnum的映射
            const typeToEnumMap = new Map<string, OperationEnum>();
            Object.values(OperationEnum).forEach(key => {
                typeToEnumMap.set(key.toLowerCase(), key as OperationEnum);
            });

            // 根据rows中的type过滤和排序操作列表
            const filteredOperations = res.rows
                            .filter((row: any) => row.type && typeToEnumMap.has(row.type.toLowerCase()))
                            .map((row: any) => {
                                const operationType = typeToEnumMap.get(row.type.toLowerCase());
                                const option = operationOptions.find(op => op.key === operationType);
                                if (option) {
                                    return {
                                        ...option,
                                        count: row.executionHistory ? row.executionHistory.length : 0,
                                        // 保存历史记录数据
                                        history: row.executionHistory || [],
                                    };
                                }
                                return null;
                            })
                            .filter(Boolean)
                            .filter((op: any) => {
                                // 如果是药品资料同步相关操作，根据region进行筛选
                                if (op.key === OperationEnum.SYNC_DRUG_INFO_GB || op.key === OperationEnum.SYNC_DRUG_INFO_JINAN) {
                                    // 如果region是济南，则只显示济南版本；否则显示国标版本
                                    return commonOperationState.isJinan.value
                                        ? op.key === OperationEnum.SYNC_DRUG_INFO_JINAN
                                        : op.key === OperationEnum.SYNC_DRUG_INFO_GB;
                                }
                                return true; // 其他操作不筛选
                            });

            // 更新操作列表，只展示在rows中存在的操作项，并按照rows的顺序排列
            operationList.value = filteredOperations;

            // 初始化历史记录缓存
            filteredOperations.forEach((op: OperationItem) => {
                if (op.history && op.history.length > 0) {
                    operationHistoryCache.value[op.key] = op.history;
                }
            });

            console.log('根据Jenkins任务列表更新的操作列表:', operationList.value);
        }
    };

    // 生成进行中操作提示文本
    const getInProgressTips = () => {
        // 使用runningTasks来获取进行中的任务
        const inProgressNames = runningTasks.value
                        .map(task => task.name)
                        .filter(Boolean);

        if (inProgressNames.length === 0) return '';

        return `当前跟进门店有如下操作还在执行中：${inProgressNames.join('、')}`;
    };

    // 加载操作历史记录
    const loadOperationHistory = async (operationType: OperationEnum) => {
        // 如果已缓存，不重复加载
        if (operationHistoryCache.value[operationType]) {
            return;
        }

        // 设置加载状态
        historyLoading.value[operationType] = true;

        try {
            // 尝试从API获取历史记录
            const res: any = await JenkinsAPI.getTaskListUsingGET(commonOperationState.clinicId.value);
            if (res?.rows && Array.isArray(res.rows)) {
                // 查找对应操作类型的历史记录
                const typeToLower = operationType.toLowerCase();
                const targetRow = res.rows.find((row: any) => row.type && row.type.toLowerCase() === typeToLower);

                if (targetRow && targetRow.executionHistory) {
                    operationHistoryCache.value[operationType] = targetRow.executionHistory;
                } else {
                    operationHistoryCache.value[operationType] = [];
                }
            } else {
                operationHistoryCache.value[operationType] = [];
            }
        } catch (error) {
            console.error('获取操作历史记录失败:', error);
            operationHistoryCache.value[operationType] = [];
        } finally {
            historyLoading.value[operationType] = false;
        }
    };

    return {
        operationList,
        loading,
        operationHistoryCache,
        historyLoading,
        runningTasks,
        currentOperation,
        currentFormConfig,
        fetchJenkinsTaskList,
        fetchRunningTasks,
        getInProgressTips,
        loadOperationHistory,
        setCurrentOperation,
    };
};

/**
 * 格式化操作内容
 * @param {Record<string, any>} row - 行数据
 * @returns {string} - 格式化后的操作内容
 */
export const formatOperationContent = (row: Record<string, any>): string => {
    try {
        if (!row.params) return '-';

        // 尝试解析JSON字符串
        const params = typeof row.params === 'string' ? JSON.parse(row.params) : row.params;

        // 将对象转换为键值对形式显示
        return Object.entries(params)
                        .map((item) => `${item[0]}=${item[1]}`)
                        .join('，');
    } catch (error) {
        // 如果解析失败，则返回原始内容
        return row.params || '-';
    }
};

// 状态标签类型映射
export const getStatusType = (status: number | string): 'success' | 'warning' | '' | 'danger' | '' => {
    // 如果是数字状态码
    if (typeof status === 'number') {
        const statusMap: Record<number, 'success' | 'warning' | '' | 'danger' | ''> = {
            0: '', // 未开始
            10: '', // 执行中
            20: 'success', // 成功
            30: 'warning', // 取消
            40: 'danger', // 失败
        };
        return statusMap[status] || '';
    }

    // 如果是字符串状态（兼容旧格式）
    const stringStatusMap: Record<string, 'success' | 'warning' | '' | 'danger' | ''> = {
        执行中: '',
        成功: 'success',
        失败: 'danger',
    };

    return stringStatusMap[status as string] || '';
};

// 状态文本格式化
export const formatStatus = (status: number | string): string => {
    // 如果是数字状态码
    if (typeof status === 'number') {
        const statusMap: Record<number, string> = {
            0: '等待执行',
            10: '执行中',
            20: '成功',
            30: '已取消',
            40: '失败',
        };
        return statusMap[status] || '未知';
    }

    // 如果是字符串状态，直接返回
    return status as string || '未知';
};
