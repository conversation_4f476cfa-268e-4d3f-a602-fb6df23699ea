<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';
import { usePrintConfigStore } from '@/views/implementation-tool/print-config/store';
import _ from 'lodash';
import Draggable from 'vuedraggable';
import { PrinterDriverAPI } from '@/api/printer-driver-api';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
});

const emit = defineEmits(['update:modelValue']);

const cacheBrandOptions = ref<any>([]);
const newBrandName = ref('');
const showNewBrandInput = ref(false);
const brandContainerRef = ref();
const newBrandInputRef = ref();

const printConfigStore = usePrintConfigStore();
const currentBrandOptions = computed(() => printConfigStore.currentBrandOptions);

const visible = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

function handleOpen() {
    cacheBrandOptions.value = _.cloneDeep(currentBrandOptions.value);
}

function handleClose() {
    showNewBrandInput.value = false;
    newBrandName.value = '';
}

async function handleSubmit() {
    try {
        const params = {
            brandList: cacheBrandOptions.value,
        };
        await PrinterDriverAPI.saveBrandForAllUsingPOST(params);
        ElMessage.success('保存成功');
    } catch (e: any) {
        console.error(e);
        ElMessage.error('保存失败，原因：' + e.message);
    } finally {
        await printConfigStore.fetchBrandOptions();
        visible.value = false;
    }
}

async function handleAddBrand() {
    showNewBrandInput.value = true;
    await nextTick();
    newBrandInputRef.value.focus();
    brandContainerRef.value.scrollTop = 9999;
}

function handleNewBrandInputBlur() {
    if (newBrandName.value) {
        cacheBrandOptions.value = [...cacheBrandOptions.value, { name: newBrandName.value }];
        newBrandName.value = '';
    }
    showNewBrandInput.value = false;
}

async function handleDeleteBrand(brandId: string) {
    const res: any = await PrinterDriverAPI.checkBrandIsUsedUsingGET(brandId);
    if (res.haveDriver) {
        ElMessageBox.alert('该品牌下已关联驱动，请先删除该品牌下的驱动后才可删除该品牌', '无法删除该品牌', {
            confirmButtonText: '知道了',
            type: 'warning',
        });
        return;
    }

    const tempBrandOptions = _.cloneDeep(cacheBrandOptions.value);
    const index = tempBrandOptions.findIndex((it: any) => it.id === brandId);
    if (index > -1) {
        tempBrandOptions.splice(index, 1);
        cacheBrandOptions.value = tempBrandOptions;
    }
}
</script>

<template>
    <el-dialog
        v-model="visible"
        title="编辑打印机品牌"
        append-to-body
        :width="500"
        class="edit-brand-dialog-wrapper"
        @open="handleOpen"
        @close="handleClose"
    >
        <div ref="brandContainerRef" class="edit-brand-dialog-content">
            <draggable v-model="cacheBrandOptions" item-key="id">
                <template #item="{element}">
                    <div class="edit-brand-dialog-brand-list edit-brand-dialog-brand-list-hover">
                        <span>{{ element.name }}</span>

                        <el-popconfirm title="确认删除？" @confirm="handleDeleteBrand(element.id)">
                            <template #reference>
                                <el-icon class="edit-brand-dialog-delete-icon">
                                    <Delete />
                                </el-icon>
                            </template>
                        </el-popconfirm>
                    </div>
                </template>
            </draggable>
            <div v-if="showNewBrandInput" class="edit-brand-dialog-brand-list">
                <el-input ref="newBrandInputRef" v-model="newBrandName" @blur="handleNewBrandInputBlur"></el-input>
            </div>
        </div>

        <template #footer>
            <div class="edit-brand-dialog-footer-wrapper">
                <el-button
                    type="primary"
                    text
                    icon="Plus"
                    @click="handleAddBrand"
                >
                    新增
                </el-button>
                <div class="edit-brand-dialog-footer-btn">
                    <el-button type="primary" @click="handleSubmit">
                        保存
                    </el-button>
                    <el-button @click="visible = false">取消</el-button>
                </div>
            </div>
        </template>
    </el-dialog>
</template>

<style lang="scss">
.edit-brand-dialog-wrapper {
    .el-dialog__body {
        padding: 0 !important;
    }
}

.edit-brand-dialog-content {
    width: 100%;
    max-height: 200px;
    overflow: scroll;
}

.edit-brand-dialog-brand-list {
    display: flex;
    width: 100%;
    height: 30px;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    cursor: pointer;
}

.edit-brand-dialog-brand-list-hover:hover {
    background-color: #eff1f5;
}

.edit-brand-dialog-delete-icon {
    &:hover {
        color: #409eff;
    }
}

.edit-brand-dialog-footer-wrapper {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
}

.edit-brand-dialog-footer-btn {
    display: flex;
    align-items: center;
}
</style>
