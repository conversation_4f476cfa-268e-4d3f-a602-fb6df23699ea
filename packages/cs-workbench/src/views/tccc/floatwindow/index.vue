<template>
    <div class="video-call">
        <!-- 来电界面 -->
        <div v-if="!isInCall" class="incoming-call">
            <div class="background-blur"></div>
            <el-card class="call-info" shadow="hover">
                <h2>有来电</h2>
                <p>点击接听开始通话</p>
                <div class="buttons">
                    <el-button
                        type="danger"
                        circle
                        size="large"
                        @click="endCall"
                    >
                        <el-icon><CloseBold /></el-icon>
                    </el-button>
                    <el-button
                        type="success"
                        circle
                        size="large"
                        @click="acceptCall"
                    >
                        <el-icon><Phone /></el-icon>
                    </el-button>
                </div>
            </el-card>
        </div>

        <!-- 通话中界面 -->
        <div v-else class="in-call">
            <div class="background-blur"></div>
            <el-card class="call-info" shadow="hover">
                <h2>通话中</h2>
                <p>正在与 {{ callerName }} 通话</p>
                <div class="buttons">
                    <el-button
                        :type="isMuted ? 'info' : 'default'"
                        circle
                        size="large"
                        @click="muteCall"
                    >
                        <el-icon>
                            <Microphone v-if="isMuted" />
                            <Mute v-else />
                        </el-icon>
                    </el-button>
                    <el-button
                        type="danger"
                        circle
                        size="large"
                        @click="endCall"
                    >
                        <el-icon><CloseBold /></el-icon>
                    </el-button>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script>
import { ref } from 'vue';

export default {
    name: 'VideoCall',
    setup() {
        const tccc = {
            startCall: () => {}, // 假设开始通话的方法
            endCall: () => {}, // 假设结束通话的方法
            on: () => {}, // 假设监听事件的方法
            muteCall() {

            },
        };
        const isInCall = ref(false); // 当前是否在通话中
        const isMuted = ref(false); // 当前是否静音
        const callerName = ref(''); // 来电者名称

        // 接听电话
        const acceptCall = () => {
            isInCall.value = true;
            callerName.value = '客服'; // 假设来电者为客服，实际应用中根据 SDK 获取来电者信息
            tccc.startCall({ userId: 'user123', callType: 'audio' });
        };

        // 挂断电话
        const endCall = () => {
            isInCall.value = false;
            callerName.value = '';
            tccc.endCall();
        };

        // 静音电话
        const muteCall = () => {
            isMuted.value = !isMuted.value;
            // 假设 SDK 提供了静音方法
            tccc.muteCall();
        };

        // 监听来电事件
        tccc.on('incomingCall', (caller) => {
            callerName.value = caller.name;
            isInCall.value = false;
        });

        return {
            isInCall,
            isMuted,
            callerName,
            acceptCall,
            endCall,
            muteCall,
        };
    },
};
</script>

<style scoped>
.video-call {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-family: Arial, sans-serif;
}

.background-blur {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .6);
    backdrop-filter: blur(15px);
    z-index: -1;
}

.call-info {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, .1);
    border-radius: 12px;
}

h2 {
    font-size: 24px;
    color: #fff;
    margin-bottom: 10px;
}

p {
    font-size: 16px;
    color: #fff;
    margin-bottom: 20px;
}

.buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}
</style>
