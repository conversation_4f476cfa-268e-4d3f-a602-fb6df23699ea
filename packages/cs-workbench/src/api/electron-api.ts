import BaseAPI from '../../../oa/src/api/base-api.ts';
import { ElMessage } from 'element-plus/es';

/**
 * 远程相关controller
 */
export class ElectronApi extends BaseAPI {
    static async connectDesktopSession(address: string, session: string, title: string): Promise<{ errorCode: number, msg: string }> {
        let res: any = {};
        // @ts-ignore
        if (!window.electron || !window.electron.slsdk) {
            return { errorCode: 400, msg: '未读取到终端' };
        }
        try {
            // @ts-ignore
            res = await window.electron.slsdk.connectDesktopSession({ address, session, title });
        } catch (e: any) {
            ElMessage.error('远程链接失败：', e.message || e);
        }
        return res;
    }

    static async setListenRemotely() {
        // @ts-ignore
        const slsdk = window.electron.slsdk;

        // @ts-ignore
        if (!window.electron || !slsdk) {
            return { errorCode: 400, msg: '未读取到终端' };
        }
        slsdk.on('desktop-session', (session: string, event: any) => {
            const sessionEventMap = new Map([
                [slsdk.ESLSessionEvent.eSLSessionEvent_OnConnected, '会话已连接成功'],
                [slsdk.ESLSessionEvent.eSLSessionEvent_OnDisconnected, '会话已断开'],
                [slsdk.ESLSessionEvent.eSLSessionEvent_OnDisplayChanged, '会话分辨率变更'],
                [slsdk.ESLSessionEvent.eSLSessionEvent_OnNewFiletrans, '收到新文件'],
                [slsdk.ESLSessionEvent.eSLSessionEvent_OnGetRemoteSysinfo, '获取到远端系统v信息'],
                [slsdk.ESLSessionEvent.eSLSessionEvent_OnRunNewInstance, '开启一个新的实例'],
                [slsdk.ESLSessionEvent.eSLSessionEvent_OnScreenshotData, '收到截图数据'],
            ]);
            return `${sessionEventMap.get(event)}: ${session}`;
        });
    }
}
