import BaseAPI from './base-api';

/**
* 
*/
export class AfterSalesA<PERSON> extends BaseAPI {
    /**
    * 获取客户通道列表
    */
    static getApiLowCodeAfterSalesAccount(
    ) {
        return this.get<AbcAPI.KfAccount>('/api/low-code/after-sales/account');
    }
    
    /**
    * 获取客户接待人列表
    */
    static getApiLowCodeAfterSalesServicer(
    ) {
        return this.get<AbcAPI.KfServicerSchedule>('/api/low-code/after-sales/servicer');
    }
    
    /**
    * 获取会话列表
    * @param {string} clinicId -     
    * @param {string} accountId -     
    * @param {string} servicerId -     
    * @param {string} keyword -     
    * @param {string} auditStatus -     
    * @param {string} auditLevel -     
    * @param {Array<number>} serviceScores -     
    * @param {any} beginDate -     
    * @param {any} endDate -     
    * @param {string} tagId - 咨询分类Id    
    * @param {string} offset -     
    * @param {string} limit -     
    */
    static getApiLowCodeAfterSalesList(
        clinicId?:string,
        accountId?:string,
        servicerId?:string,
        keyword?:string,
        auditStatus?:string,
        auditLevel?:string,
        serviceScores?:Array<number>,
        beginDate?:any,
        endDate?:any,
        tagId?:string,
        offset?:string,
        limit?:string,
    ) {
        return this.get<Array<AbcAPI.KfServicerSchedule>>('/api/low-code/after-sales/list', {
            params: {
                clinicId,
                accountId,
                servicerId,
                keyword,
                auditStatus,
                auditLevel,
                serviceScores,
                beginDate,
                endDate,
                tagId,
                offset,
                limit,
            },
        });
    }
    
    /**
    * 获取当前会话记录
    * @param {string} clientId - 客户ID    
    * @param {string} servicerId - 接待人ID    
    */
    static getApiLowCodeAfterSales(
        clientId?:string,
        servicerId?:string,
    ) {
        return this.get<AbcAPI.KfServicerSchedule>('/api/low-code/after-sales', {
            params: {
                clientId,
                servicerId,
            },
        });
    }
    
    /**
    * 根据群聊ID获取对应数量的群聊记录
    * @param {string} roomId - 群聊ID    
    * @param {string} qwCorpId - 企业微信ID    
    * @param {number} limit - 记录条数    
    */
    static getApiLowCodeAfterSalesRoomListByLimit(
        roomId:string,
        qwCorpId?:string,
        limit?:number,
    ) {
        return this.get<any>('/api/low-code/after-sales/room/list/by-limit', {
            params: {
                qwCorpId,
                roomId,
                limit,
            },
        });
    }
    
    /**
    * 获取会话记录详情
    * @param {string} conversationId -     
    */
    static getApiLowCodeAfterSalesDetail(
        conversationId:string,
    ) {
        return this.get<AbcAPI.KfMsgDetail>('/api/low-code/after-sales/detail', {
            params: {
                conversationId,
            },
        });
    }
    
    /**
    * 审批会话记录
    * @param {AbcAPI.SetAuditResultsDto} setAuditResultsDto -     
    */
    static postApiLowCodeAfterSalesAudit(
        setAuditResultsDto:AbcAPI.SetAuditResultsDto,
    ) {
        return this.post<AbcAPI.KfConversationServicer>(
            '/api/low-code/after-sales/audit',
            setAuditResultsDto,
        );
    }
    
    /**
    * 获取会话问题标注tag
    * @param {string} conversationId -     
    * @param {string} servicerId -     
    * @param {string} tagId -     
    * @param {number} isHighFrequency -     
    */
    static getApiLowCodeAfterSalesConversationTag(
        conversationId:string,
        servicerId:string,
        tagId?:string,
        isHighFrequency?:number,
    ) {
        return this.get<AbcAPI.KfTag>('/api/low-code/after-sales/conversation/tag', {
            params: {
                conversationId,
                servicerId,
                tagId,
                isHighFrequency,
            },
        });
    }
    
    /**
    * 设置会话问题标注tag
    * @param {AbcAPI.SetConversationTagDto} setConversationTagDto -     
    */
    static postApiLowCodeAfterSalesConversationTag(
        setConversationTagDto:AbcAPI.SetConversationTagDto,
    ) {
        return this.post<AbcAPI.KfTag>(
            '/api/low-code/after-sales/conversation/tag',
            setConversationTagDto,
        );
    }
    
    /**
    * 根据客户id获取客户设备
    * @param {string} employeeId -     
    */
    static getApiLowCodeAfterSalesDevicesByEmployee(
        employeeId:string,
    ) {
        return this.get<AbcAPI.FindDevicesByEmployeeVo>('/api/low-code/after-sales/devices/by-employee', {
            params: {
                employeeId,
            },
        });
    }
    
    /**
    * 咨询分类数量排名(由高到低)
    * @param {number} top -     
    * @param {string} endDate -     
    * @param {string} beginDate -     
    * @param {string} accountId -     
    */
    static getApiLowCodeAfterSalesConversationTagTop(
        top:number,
        endDate:string,
        beginDate:string,
        accountId:string,
    ) {
        return this.get<AbcAPI.ConversationTagStatVo>('/api/low-code/after-sales/conversation/tag/top', {
            params: {
                top,
                endDate,
                beginDate,
                accountId,
            },
        });
    }
    
    /**
    * 创建tapd单
    * @param {AbcAPI.CreatedDataDto} createdDataDto -     
    */
    static postApiLowCodeTapd(
        createdDataDto:AbcAPI.CreatedDataDto,
    ) {
        return this.post<AbcAPI.CrmListVo>(
            '/api/low-code/tapd',
            createdDataDto,
        );
    }
    
    /**
    * 获取Tapd指定项目用户列表
    */
    static getApiLowCodeTapdWorkspaceUsers(
    ) {
        return this.get<AbcAPI.FindWorkspaceUsersVo>('/api/low-code/tapd/workspace/users');
    }
    
    /**
    * 会话AI总结
    * @param {object} params - 请求参数
    * @param {string} params.conversationId - 会话ID
    * @param {string} params.servicerId - 接待人ID
    */
    static postApiLowCodeAfterSalesAiSummary(
        params: {
            conversationId: string;
            servicerId: string;
        },
    ) {
        return this.post<{
            success: boolean;
            message?: string;
        }>(
            '/api/low-code/after-sales/ai-summary',
            params,
        );
    }
}