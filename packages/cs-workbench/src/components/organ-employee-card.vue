<script setup lang="ts">
import { Avatar } from '@element-plus/icons-vue';
import { computed } from 'vue';

const props = defineProps({
    employee: {
        type: Object,
        required: true,
    },
});

const displayEmployee = computed(() => {
    const employee = props.employee;
    return {
        ...employee,
    };
});
</script>
<template>
    <div class="organ-employee-card-wrapper">
        <van-row>
            <van-col :span="4">
                <el-avatar shape="square" :src="displayEmployee.employeeHeadImgUrl">
                    <el-icon size="48"><avatar /></el-icon>
                </el-avatar>
            </van-col>
            <van-col :span="20">
                <van-row>
                    {{ displayEmployee.employeeName }}
                    {{ displayEmployee.employeeMobile }}
                    <span class="role">{{ displayEmployee.role }}</span>
                </van-row>
                <van-row wrap> {{ displayEmployee.clinicName }} </van-row>
            </van-col>
        </van-row>
    </div>
</template>

<style lang="scss">
.organ-employee-card-wrapper {
    padding: 10px 16px;

    .title {
        font-weight: bold;
        font-size: 16px;
    }

    .role {
        color: var(--oa-text-color-2);
        margin-left: 8px;
    }
}
</style>
