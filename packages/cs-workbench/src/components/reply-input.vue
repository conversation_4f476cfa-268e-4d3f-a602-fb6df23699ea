<script lang="ts" setup>

import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, ElImageViewer } from 'element-plus/es';
import OSSUtil from '@/utils/oss';

const props = defineProps({
    editorClass: {
        type: String,
        default: '',
    },
    editorStyle: {
        type: Object,
        default: () => ({}),
    },
    imgShowWidth: { // 聊天输入框中粘贴的图片显示的宽度
        type: Number,
        default: 100,
    },
    imgShowHeight: { // 聊天输入框中粘贴的图片显示的高度
        type: Number,
        default: 100,
    },
    name: { // 上传 表单 name
        type: String,
        default: 'upload',
    },
    bucket: {
        type: String,
        default: 'VITE_APP_OSS_BUCKET',
    },
    rootDir: {
        type: String,
        default: 'oa',
    },
    enter: { // 是否支持回车
        type: Boolean,
        default: false,
    },
    showSubmitBtn: { // 是否显示发送按钮
        type: Boolean,
        default: true,
    },
    chatInputValue: {
        type: Array,
        default: () => [],
    },
    echoValue: {
        type: Array,
        default: () => [],
    },
    isWatchValue: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: '请输入',
    },
});
const msgList = ref<any[]>([]);
const loading = ref(false);
const handlePaste = async (event: any) => {
    const pasteResult = handlePastePlainText(event.clipboardData);
    if (pasteResult) return;
    await handlePasteFile(event.clipboardData);
};
const emit = defineEmits(['enter', 'change']);
const submitLoading = ref(false);
const handleKeyUp = (event: any) => {
    submitLoading.value = true;
    const childNodes = event.target.childNodes;
    emitChange(childNodes);
    if (event.keyCode === 13 && !props.enter) {
        emit('enter');
    }
    submitLoading.value = false;
};
const handleSubmit = () => {
    emit('enter');
};
const handleKeyDown = (event: any) => {
    if (event.keyCode === 13) { // 禁止换行默认行为
        event.preventDefault();
        if (props.enter) {
            const oBr = document.createElement('br');
            cursorInsert(oBr);
        }
    }
};
/**
 * @description: 去格式粘贴 文本
 * @date: 2023-11-24 16:55:01
 * @author: Horace
 * @param {any} clipboardData 剪贴板数据
 * @return
*/
const handlePastePlainText = (clipboardData: any) => {
    const text = clipboardData.getData('text/plain');
    if (text) {
        insertAndEmitChange(document.createTextNode(text));
        return true;
    }
    return false;
};
const editor = ref<any>(null);

/**
 * @description: 处理粘贴文件
 * @date: 2024-06-12 19:52:07
 * @author: Horace
 * @param {any} clipboardData 剪贴板数据
 * @return
*/
const handlePasteFile = async (clipboardData: any) => {
    const file:any = getPasteFile(clipboardData.files);
    if (!file) return;
    const isImage = file.type.match(/image\//i);
    let uploadRes: any = {};
    try {
        uploadRes = isImage ? await uploadChatImg(file) : await uploadChatFile(file);
    } catch (e) {
        console.error('文件上传失败', e);
        ElMessage.error('文件上传失败,请重新上传');
        return;
    }
    const oFile = isImage
        ? await getImageObject(uploadRes, props.imgShowWidth, props.imgShowHeight)
        : await getFileObject(uploadRes, props.imgShowWidth, props.imgShowHeight);
    insertAndEmitChange(oFile);
};

/**
 * @description: 插入节点并触发change事件
 * @date: 2024-06-12 19:51:50
 * @author: Horace
 * @param {any} node 节点
 * @return
*/
const insertAndEmitChange = (node: any) => {
    cursorInsert(node);
    const oEditor = editor.value;
    emitChange(oEditor.childNodes);
};
/**
 * @description: 输入框发生变化
 * @date: 2023-11-26 16:27:43
 * @author: Horace
 * @param {any} editorChildNodes 输入框子节点
 * @param {boolean} isDeep 是否深度遍历
 * @return
*/
const emitChange = (editorChildNodes: any, isDeep = false) => {
    const oldMsgList = JSON.parse(JSON.stringify(msgList.value));
    msgList.value = []; // 重置
    for (let i = 0; i < editorChildNodes.length; i++) {
        const childNode = editorChildNodes[i];
        if (childNode.nodeType === 1 && childNode.nodeName === 'BR') { // 处理回车
            const lastMsg = msgList.value[msgList.value.length - 1];
            if (lastMsg?.type === 'text') {
                lastMsg.content += '\n';
            }
        } else if (childNode.nodeType === 3 && childNode.nodeValue) {
            const lastMsg = msgList.value[msgList.value.length - 1];
            if (lastMsg?.type === 'text') {
                lastMsg.content += childNode.nodeValue;
            } else {
                msgList.value.push({
                    type: 'text',
                    content: childNode.nodeValue,
                });
            }
        } else if (childNode.nodeType === 1 && childNode.nodeName === 'IMG') {
            const dataset = childNode.dataset;
            msgList.value.push({
                type: 'image',
                url: childNode.src,
                width: dataset.width,
                height: dataset.height,
                filename: dataset.filename,
                fileType: dataset.type,
            });
        } else if (childNode.nodeType === 1 && childNode.nodeName === 'A') {
            const style = childNode.style;
            const dataset = childNode.dataset;
            msgList.value.push({
                type: dataset.type.match(/video\//i) ? 'video' : 'file',
                url: childNode.href,
                width: style.width,
                height: style.height,
                filename: childNode.textContent,
            });
        } else if (childNode.childNodes && childNode.childNodes.length) {
            const childNodes = childNode.childNodes;
            emitChange(childNodes, true);
        }
    }
    if (!msgList.value.length && !oldMsgList.length) {
        return;
    }
    msgList.value = isDeep ? [...oldMsgList, ...msgList.value] : msgList.value;
    emit('change', [...msgList.value]);
};

/**
 * @description: 光标处插入节点
 * @date: 2023-11-24 16:59:08
 * @author: Horace
 * @param {any} node 节点
 * @return
*/
const cursorInsert = (node: any) => {
    // 获取光标范围
    const selObj: any = window.getSelection();
    const range = selObj.getRangeAt(0);
    // 光标处插入节点
    range.insertNode(node);
    // 取消insert node 后的选中状态，将光标恢复到 insert node 后面
    range.collapse(false);
};
/**
 * @description: 获取用户剪贴板第一个文件
 * @date: 2023-11-26 15:41:22
 * @author: Horace
 * @param {any} clipboardDataFiles 剪贴板文件
 * @return
*/
const getPasteFile = (clipboardDataFiles:any) => {
    if (!clipboardDataFiles.length) {
        console.log('没有要粘贴的文件');
        return null;
    }
    // 剪切版中选择的第一个文件
    const clipboardDataFileList = Array.from(clipboardDataFiles || []);
    let firstSelectedImage = null;
    clipboardDataFileList.forEach((file: any) => {
        firstSelectedImage = file;
    });
    return firstSelectedImage;
};
const fileLink = ref('');
/**
 * @description: 上传图片
 * @date: 2023-11-26 16:26:43
 * @author: Horace
 * @param {any} file 图片文件
 * @return
*/
const uploadChatImg = async (file: any) => {
    const formData = new FormData();
    formData.append(props.name, file);
    loading.value = true;
    let res: any = {};
    try {
        res = await OSSUtil.upload({
            bucket: import.meta.env.VITE_APP_OSS_BUCKET,
            region: import.meta.env.VITE_APP_OSS_REGION,
            rootDir: 'oa/advertisement/file',
        }, file);
        fileLink.value = res.url;
    } catch (e) {
        console.error('file 上传失败', e);
    }
    return { filename: file.name, type: file.type, ...res };
};
/**
 * @description: 上传文件
 * @date: 2023-11-26 16:26:53
 * @author: Horace
 * @param {any} file 文件
 * @return
*/
const uploadChatFile = async (file: any) => {
    try {
        const { url } = await OSSUtil.upload({
            bucket: import.meta.env[props.bucket] || import.meta.env.VITE_APP_OSS_BUCKET,
            region: import.meta.env.VITE_APP_OSS_REGION,
            rootDir: props.rootDir || 'oa',
            fileName: file.name || '',
        }, file);
        file.status = 'done';
        file.message = '';
        file.url = url;
    } catch (e) {
        console.error('e', e);
        file.status = 'failed';
        file.message = '上传失败';
        return null;
    }
    return file;
};
/**
 * @description: 获取图片dom
 * @date: 2023-11-26 16:27:28
 * @author: Horace
 * @param {any} uploadRes 上传结果
 * @param {number} showWidth 显示宽度
 * @param {number} showHeight 显示高度
 * @return
*/
const getImageObject = (uploadRes: any, showWidth: number, showHeight: number) => {
    const oImage = new Image(showWidth, showHeight);
    const datasetFields = ['width', 'height', 'fileName', 'type'];
    datasetFields.forEach(field => {
        oImage.setAttribute(`data-${field}`, uploadRes[field]);
    });
    oImage.src = uploadRes.url || uploadRes.path;
    
    // 添加图片点击预览事件
    oImage.style.cursor = 'pointer';
    oImage.onclick = () => {
        previewImageSrcList.value = [uploadRes.url || uploadRes.path];
        initialIndex.value = 0;
        showPreviewImage.value = true;
    };
    
    return oImage;
};
/**
 * @description: 获取文件dom
 * @date: 2023-11-26 16:27:15
 * @author: Horace
 * @param {any} uploadRes 上传结果
 * @param {number} showWidth 显示宽度
 * @param {number} showHeight 显示高度
 * @return
*/
const getFileObject = (uploadRes: any, showWidth: number, showHeight: number) => {
    const divElement = document.createElement('div');
    const fileNameElement = document.createElement('a');
    const fileName = uploadRes.name;
    fileNameElement.textContent = fileName;
    fileNameElement.className = 'reply-upload-file-icon';
    fileNameElement.style.display = 'inline-block';
    divElement.style.display = 'inline-block';
    divElement.contentEditable = 'false';
    fileNameElement.style.height = showHeight + 'px';
    fileNameElement.style.width = showWidth + 'px';
    fileNameElement.href = uploadRes.url;
    fileNameElement.download = fileName;
    fileNameElement.target = '_block';
    const datasetFields = ['fileName', 'type'];
    datasetFields.forEach(field => {
        fileNameElement.setAttribute(`data-${field}`, uploadRes[field]);
    });
    divElement.appendChild(fileNameElement);
    return divElement;
};
// 清除 输入框
const clean = () => {
    editor.value.innerHTML = '';
};
const currentValue = computed(() => props.chatInputValue);
watch(currentValue, () => {
    if (!props.isWatchValue) {
        return;
    }
    if (currentValue.value && Array.isArray(currentValue.value) && currentValue.value.length && editor.value) {
        currentValue.value.forEach((item: any) => {
            item.msgType === 'text' && (editor.value.innerHTML = item.msgText.content);
        });
    }
});
defineExpose({ clean });
// 输入框 焦点
const focus = () => {
    editor.value.focus();
};
const showPreviewImage = ref(false);
const previewImageSrcList = ref<string[]>([]);
const initialIndex = ref(0);
const echoFormatter = ((value: any, fragment: any, inputValue: any[]) => {
    if (value.msgType === 'text') {
        const temp = document.createTextNode(value.msgText.content);
        fragment.appendChild(temp);
        inputValue.push({
            type: 'text',
            content: value.msgText.content,
        });
    }
    if (value.msgType === 'image') {
        const oImage = new Image(props.imgShowWidth, props.imgShowHeight);
        oImage.setAttribute('data-type', value.msgType);
        oImage.src = value.msgImage.ossUrl;
        // 添加图片点击事件
        oImage.style.cursor = 'pointer';
        oImage.onclick = () => {
            previewImageSrcList.value = [value.msgImage.ossUrl];
            initialIndex.value = 0;
            showPreviewImage.value = true;
        };
        fragment.appendChild(oImage);
        inputValue.push({
            type: value.msgType,
            url: value.msgImage.ossUrl,
            width: props.imgShowWidth,
            height: props.imgShowHeight,
            filename: value.msgImage.filename,
            fileType: 'image',
        });
    }
    if (value.msgType === 'file' || value.msgType === 'video') {
        const uploadRes = {
            name: value.msgFile?.filename || '',
            url: value.msgFile?.ossUrl || value.msgVideo.ossUrl,
            filename: value.msgFile?.filename || '',
            type: value.msgType,
        };
        const oFile = getFileObject(uploadRes, props.imgShowWidth, props.imgShowHeight);
        fragment.appendChild(oFile);
        inputValue.push({
            type: value.msgType,
            url: value.msgFile?.ossUrl || value.msgVideo.ossUrl,
            width: props.imgShowWidth,
            height: props.imgShowHeight,
            filename: value.msgFile?.filename || '',
            fileType: value.msgType,
        });
    }
});
onMounted(() => {
    if (props.echoValue?.length) {
        // 创建一个文档片段
        const fragment = document.createDocumentFragment();
        const inputValue: any[] = [];
        props.echoValue.forEach((item: any) => echoFormatter(item, fragment, inputValue));
        const editorDom: any = document.getElementById('editor');
        editorDom.appendChild(fragment);
        emitChange(editorDom.childNodes);
        // emit('change', inputValue);
    }
});
</script>
<template>
    <div class="reply-input-wrapper">
        <div class="left">
            <div
                id="editor"
                ref="editor"
                contenteditable="true"
                :class="editorClass"
                :style="editorStyle"
                @paste.prevent="handlePaste($event)"
                @keyup="handleKeyUp($event)"
                @keydown="handleKeyDown($event)"
            >
            </div>
            <div><i v-show="loading" class="el-icon-loading" /></div>
            <div v-if="placeholder && !currentValue?.length" class="reply-input__placeholder">{{ placeholder }}</div>
        </div>
        <el-button
            v-if="showSubmitBtn"
            class="reply-input-btn"
            type="primary"
            :disabled="!currentValue?.length"
            :loading="submitLoading"
            @click="handleSubmit"
        >
            发送
        </el-button>
        <!-- 图片预览组件 -->
        <el-image-viewer
            v-if="showPreviewImage"
            :url-list="previewImageSrcList"
            :initial-index="initialIndex"
            @close="showPreviewImage = false"
        />
    </div>
</template>
<style scoped lang="scss">
.reply-input-wrapper {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    border: 1px solid #dcdfe6;
    padding: 4px;
    position: relative;

    .reply-input__placeholder {
        position: absolute;
        left: 6px;
        top: 6px;
        color: var(--el-text-color-placeholder);
        pointer-events: none;
    }

    .left {
        flex: 1;

        div:nth-of-type(1) {
            padding: 4px;
            width: 300px;
            min-height: 100px;
            outline: none;
            word-break: break-all;
        }
    }

    .reply-input-btn {
        margin-left: 12px;
    }

    ::v-deep .reply-upload-file-icon {
        background-color: #fff;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 8px;
        font-size: 14px;
        color: #333;

        &::before {
            content: '\1F4C4'; /* Unicode character for a file icon */
            font-size: 48px;
            color: #3498db;
        }
    }
}
</style>
