<template>
    <div 
        class="oa-float-nav" 
        :class="{ 'oa-float-nav--hovered': isHovered }" 
        @mouseenter="handleMouseEnter" 
        @mouseleave="handleMouseLeave"
    >
        <div class="oa-float-nav__container">
            <div 
                class="oa-float-nav__trigger"
                :style="{ opacity: isHovered ? 0 : 1 }"
            ></div>
            <div 
                class="oa-float-nav__ball"
                :style="{ opacity: isHovered ? 1 : 0, transform: isHovered ? 'translateX(-20px) scale(1)' : 'translateX(0) scale(0.5)' }"
                @click="toggleRoute"
            >
                <span class="oa-float-nav__text">切换</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, inject } from 'vue';

// 通过注入获取路由器实例，而不是直接导入
const router = inject('router') as any;
const isHovered = ref(false);
let hoverTimer: number | null = null;

// 判断当前是否在CS工作台
const isInCs = computed(() => {
    if (!router) return false;
    // 检查当前URL是否包含/cs/
    return window.location.pathname.includes('/cs/');
});

// 处理鼠标进入
const handleMouseEnter = () => {
    if (hoverTimer) {
        clearTimeout(hoverTimer);
        hoverTimer = null;
    }
    isHovered.value = true;
};
    
// 处理鼠标离开
const handleMouseLeave = () => {
    // 延迟关闭，防止鼠标快速移动导致的闪烁
    hoverTimer = window.setTimeout(() => {
        isHovered.value = false;
        hoverTimer = null;
    }, 300);
};
    
// 切换路由
const toggleRoute = () => {
    if (!router) return;
    
    if (isInCs.value) {
        // 当前在CS工作台，切换到OA工作台
        window.location.href = `${window.location.origin}/dashboard`;
    } else {
        // 当前在OA工作台，切换到CS工作台
        window.location.href = `${window.location.origin}/cs/home`;
    }
};
</script>

<style scoped lang="scss">
.oa-float-nav {
    position: fixed;
    right: 0;
    bottom: 100px;
    z-index: 999;

    // 扩大整个组件的热区，确保鼠标移动时不会离开组件
    padding-left: 60px;
    padding-top: 32px;
    padding-bottom: 32px;
}

.oa-float-nav__container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.oa-float-nav__trigger {
    width: 4px;
    height: 64px;
    background-color: rgba(255, 87, 34, .8); /* 亮橙色，在浅色和深色背景下都很醒目 */
    border-radius: 2px 0 0 2px;
    cursor: pointer;
    transition: all .3s ease;
    position: absolute;
    right: 0;

    &:hover {
        background-color: rgba(255, 87, 34, 1);
    }
}

.oa-float-nav__ball {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff5722, #ff9800); /* 橙色渐变，高可见度 */
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(255, 87, 34, .4);
    transition: all .3s cubic-bezier(.34, 1.56, .64, 1);
    position: absolute;
    right: 0;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        background: radial-gradient(circle at top left, rgba(255, 255, 255, .3) 0%, rgba(255, 255, 255, 0) 60%);
        border-radius: 50%;
        transform: scale(0);
        transition: transform .5s ease;
    }

    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 50%;
        background: linear-gradient(to top, rgba(0, 0, 0, .1), transparent);
        border-radius: 0 0 30px 30px;
        opacity: .5;
    }

    &:hover {
        transform: translateX(-20px) scale(1.05) !important;
        box-shadow: 0 8px 25px rgba(255, 87, 34, .5), 0 0 0 1px rgba(255, 255, 255, .1) inset;

        &::before {
            transform: scale(1);
        }
    }

    .oa-float-nav__icon {
        margin-bottom: 4px;
        animation: pulse 2s infinite;
        font-size: 18px;
    }

    .oa-float-nav__text {
        font-weight: 600;
        letter-spacing: 1px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, .2);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

.oa-float-nav--hovered {
    .oa-float-nav__trigger {
        opacity: 0;
    }

    .oa-float-nav__ball {
        opacity: 1;
        transform: translateX(-20px) scale(1);
    }
}
</style>
