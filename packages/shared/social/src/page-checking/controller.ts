/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import dayjs from 'dayjs';
import SocialApi from '../api/social-api';
import AbcResponse from '../common/AbcResponse';
import * as utils from '../common/utils';
import { reactive, computed } from 'vue';
import createDialogModel from '../model/dialog';

import createLoadingModel from '../model/loading';

export const createPageCheckingController = () => {
    const dialogModelCheckingClinic = createDialogModel();
    const loadingModelData = createLoadingModel();

    // 工具栏参数
    const toolsParams = reactive({
        regions: [],
        dataRange: [
            dayjs().startOf('month').format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD'),
        ],
        myChart: null,
    });

    // 返回数据
    const resultData = reactive({
        region: '',
        status: 0,
        clinicStatusList: [],
    });

    const disabledDateFunc = (time: Date) => {
        if (time.getTime() > Date.now()) {
            // 不允许选择未来日期
            return true;
        }
        if (time.getTime() < dayjs().startOf('month').subtract(6, 'month').toDate().getTime()) {
            // 不允许选择6个月之前的日期
            return true;
        }
        return false;
    }

    // 是否禁用查询按钮
    const disabledQueryBtn = computed(() => {
        if (toolsParams.regions.length === 0) {
            return true;
        }
        if (toolsParams.dataRange.length === 0) {
            return true;
        }
        return false;
    });

    /**
     * 检查日期范围
     * <AUTHOR>
     * @date 2025-07-29
     * @returns {Boolean}
     */
    const checkDateRange = () => {
        const [beginDate, endDate] = toolsParams.dataRange;
        if (beginDate.slice(0, 7) !== endDate.slice(0, 7)) {
            return false;
        }
        return true
    }

    /**
     * 创建请求参数
     * <AUTHOR>
     * @date 2025-07-29
     * @returns {Object}
     */
    const createParams = () => {
        const params = {
            regionList: toolsParams.regions.reduce((list: any[], item: any[]) => {
                const region = item.slice(-1)[0]
                list.push(region);
                return list;
            }, []),
            beginDate: toolsParams.dataRange[0],
            endDate: toolsParams.dataRange[1],
        };
        return params;
    }

    /**
     * 请求结算统计状态
     * <AUTHOR>
     * @date 2025-07-29
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestSettleStatStatus = async (params: any) => {
        const {
            regionList, // 地区列表
            beginDate, // 开始日期
            endDate, // 结束日期
        } = params;
        const paramsList = (() => {
            const list = [];
            if (regionList.includes('sichuan_chengdu')) {
                list.push(['sichuan_chengdu']);
            }
            if (regionList.includes('chongqing_gb')) {
                list.push(['chongqing_gb']);
            }
            const regionListClone = regionList
                .filter((item: string) => item !== 'sichuan_chengdu' && item !== 'chongqing_gb')
                .slice()
            const step = 5;
            for (let i = 0; i < regionListClone.length; i += step) {
                list.push(regionListClone.slice(i, i + step));
            }
            return list;
        })()
        const clinicStatusList = []
        for (let i = 0; i < paramsList.length; i++) {
            const params = {
                regionList: paramsList[i], // 地区列表
                beginDate, // 开始日期
                endDate, // 结束日期
            }
            const fetchResponse = await SocialApi.fetchSettleStatStatus(params);
            if (fetchResponse.status === false) {
                return fetchResponse;
            }
            clinicStatusList.push(...(fetchResponse.data.clinicStatusList || []));
        }
        return AbcResponse.success({ clinicStatusList});
    }

    return {
        dialogModelCheckingClinic,
        loadingModelData,
        disabledQueryBtn,
        disabledDateFunc,
        toolsParams,
        resultData,
        createParams,
        checkDateRange,
        requestSettleStatStatus,
    };
};