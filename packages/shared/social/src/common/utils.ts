/*
 * <AUTHOR>
 * @DateTime 2023-11-14 17:17:50
 */
import dayjs from 'dayjs';
// import OSSUtil from './oss';
import AbcResponse from './AbcResponse';
import {
    cloneDeep as _cloneDeep,
    isEqual as _isEqual,
} from 'lodash';
import { ElMessageBox } from 'element-plus';
import { regionNameOptions } from './options';
import { exportExcelFile } from '@abc-oa/utils/src/excel';
import { isProd, isTest, isDev, isLocal } from '@abc-oa/utils/src/env';

import SocialApi from '../api/social-api';

declare global {
    interface Window {
        // 医保相关接口
        SocialApi: any;
        // 创建国标初始化示例
        createNationalInstall: any;
        // 医保加载完成函数
        loadSocialSuccess: any;
        // 环境配置，不配置会报错
        $platform: any;
        // 获取地区导出列表
        getRegionExportList: any;
        // 地区
        $national: any;
        // 远程代码执行SDK
        remoteSDK: any;
    }
}
window.SocialApi = SocialApi; // 医保相关接口

// 深拷贝
export const cloneDeep = _cloneDeep;

// 判断两个对象是否相等
export const isEqual = _isEqual;

// 创建excel文件
export const createExcelFile = exportExcelFile;

/**
 * 加法函数
 * <AUTHOR>
 * @date 2022-05-11
 * @returns {Number}
 */
export const add = (...args: any[]) => {
    if (args.length === 0) {
        return 0;
    }
    if (args.length === 1) {
        return args[0];
    }
    if (args.length === 2) {
        let [a, b] = args;
        if (!a) a = 0;
        if (!b) b = 0;
        a += '';
        b += '';
        let al = a.split('.')[1];
        let bl = b.split('.')[1];
        al = al ? al.length : 0;
        bl = bl ? bl.length : 0;
        let c = Math.abs(al - bl);
        // eslint-disable-next-line no-restricted-properties
        let m = Math.pow(10, Math.max(al, bl));
        if (c > 0) {
            // eslint-disable-next-line no-restricted-properties
            const cm = Math.pow(10, c);
            if (al > bl) {
                a = Number(a.replace('.', ''));
                b = Number(b.replace('.', '')) * cm;
            } else {
                a = Number(a.replace('.', '')) * cm;
                b = Number(b.replace('.', ''));
            }
        } else {
            a = Number(a.replace('.', ''));
            b = Number(b.replace('.', ''));
        }
        return (a + b) / m;
    }
    return args.reduce((a, b) => add(a, b));
};

/**
 * 减法函数
 * <AUTHOR>
 * @date 2022-05-11
 * @returns {Number}
 */
export const red = (...args: any[]) => {
    if (args.length === 0) {
        return 0;
    }
    if (args.length === 1) {
        return args[0];
    }
    if (args.length === 2) {
        let [a, b] = args;
        if (!a) a = 0;
        if (!b) b = 0;
        a += '';
        b += '';
        return add(a, -b);
    }
    return args.reduce((a, b) => red(a, b));
};

/**
 * 格式化金额
 * <AUTHOR>
 * @date 2025-01-11
 * @param {any} number
 * @param {any} len
 * @returns {String}
 */
export function moneyStr(num: any, len = 2) {
    num = parseFloat(num);
    // eslint-disable-next-line no-restricted-globals
    if (isNaN(num)) num = 0;
    return num.toFixed(len);
}

/**
 * 是否函数
 * <AUTHOR>
 * @date 2025-01-11
 * @param {any} val
 * @returns {Boolean}
 */
export function isFunction(val: any) {
    return typeof val === 'function';
}

/**
 * 是否数组
 * <AUTHOR>
 * @date 2025-01-11
 * @param {any} val
 * @returns {Boolean}
 */
export function isArray(val: any) {
    return Array.isArray(val);
}

/**
 * 是否字符串
 * <AUTHOR>
 * @date 2025-01-11
 * @param {any} val
 * @returns {Boolean}
 */
export function isString(val: any) {
    return typeof val === 'string';
}

/**
 * 是否数字
 * <AUTHOR>
 * @date 2025-01-11
 * @param {any} val
 * @returns {Boolean}
 */
export function isNumber(val: any) {
    return parseFloat(val).toString() != 'NaN';
}

/**
 * 判断是否对象
 * <AUTHOR>
 * @date 2020-07-09
 * @param {any} obj
 * @returns {Boolean}
 */
export function isObject(obj: any) {
    return Object.prototype.toString.call(obj) === '[object Object]';
}

/**
 * 是否空
 * <AUTHOR>
 * @date 2022-05-11
 * @param {any} value
 * @returns {Boolean}
 */
export const isEmpty = (value: any) => value === undefined || value === null || value === '';

/**
 * 延迟promise
 * <AUTHOR>
 * @date 2024-09-06
 * @param {Number} times
 * @returns {Promise}
 */
export const delayPromise = (times: number) => new Promise((resolve: Function) => {
    setTimeout(() => {
        resolve();
    }, times);
});

/**
 * 弹窗确认框，返回Promise对象，可用await接收结果
 * <AUTHOR>
 * @date 2023-11-15
 * @param {any} ...args
 * @returns {Promise<AbcResponse>}
 */
export const messageConfirm = async (...args: [string, string, any]): Promise<AbcResponse> => await new Promise((resolve) => {
    ElMessageBox.confirm(...args)
                    .then(() => {
                        resolve(AbcResponse.success());
                    })
                    .catch(() => {
                        resolve(AbcResponse.error('cancel'));
                    });
});

/**
 * 获取归类展示信息
 * <AUTHOR>
 * @date 2023-11-10
 * @param {Object} classifyItem
 * @returns {String}
 */
export const getClassifyWording = (classifyItem: any) => {
    const includes = classifyItem?.includes || [];
    const checkCode = classifyItem?.checkCode || '';
    let wording = '';
    if (checkCode) {
        wording = `代码检查：${checkCode.replace(/\n/g, '')}`;
    } else if (includes.length !== 0) {
        wording = `内容包含：${includes.join('、')}`;
    } else {
        //
    }
    return wording;
};

/**
 * 获取标签展示信息
 * <AUTHOR>
 * @date 2023-11-10
 * @param {Object} classifyItem
 * @returns {String}
 */
export const getLabelWording = (classifyItem: any) => (classifyItem?.labels || []).join('、');

/**
* 创建日志仓库名称选项
* <AUTHOR>
* @date 2023-08-08
* @returns {Array}
*/
export const createLogStoreOptions = (): Array<any> => {
    const options = [
        { isCurEnv: isDev || isLocal, label: '短日志', value: 'dev', isLongtime: false, isLogDefaultValue: false, isLogServeDefaultValue: true },
        { isCurEnv: isDev || isLocal, label: '长日志', value: 'dev_longtime', isLongtime: true, isLogDefaultValue: true, isLogServeDefaultValue: false },
        { isCurEnv: isTest, label: '短日志', value: 'test', isLongtime: false, isLogDefaultValue: false, isLogServeDefaultValue: true },
        { isCurEnv: isTest, label: '长日志', value: 'test_longtime', isLongtime: true, isLogDefaultValue: true, isLogServeDefaultValue: false },
        { isCurEnv: isProd, label: '短日志', value: 'prod', isLongtime: false, isLogDefaultValue: false, isLogServeDefaultValue: true },
        { isCurEnv: isProd, label: '长日志', value: 'prod_longtime', isLongtime: true, isLogDefaultValue: true, isLogServeDefaultValue: false },
    ];
    return options.filter(item => item.isCurEnv === true);
};

/**
* 创建日志仓库名称选项
* <AUTHOR>
* @date 2023-08-08
* @returns {Array}
*/
export const createRegionOptions = (): Array<any> => {
    const options = [
        { isCurEnv: isDev || isLocal, label: '上海', value: 'region1' },
        { isCurEnv: isTest, label: '上海', value: 'region1' },
        { isCurEnv: isProd, label: '上海', value: 'region1' },
        { isCurEnv: isProd, label: '杭州', value: 'region2' },
    ];
    return options.filter(item => item.isCurEnv === true);
};

/**
* 创建Bug日志仓库名称选项
* <AUTHOR>
* @date 2023-08-08
* @returns {Array}
*/
export const createBugLogStoreOptions = (): Array<any> => {
    const options = [
        { isCurEnv: isDev || isLocal, value: 'dev-metrics-processed' },
        { isCurEnv: isTest, value: 'test-metrics-processed' },
        { isCurEnv: isProd, value: 'prod-metrics-processed' },
    ];
    return options.filter(item => item.isCurEnv === true);
};

/**
 * 创建自定义参数
 * <AUTHOR>
 * @date 2025-01-24
 * @returns {Object}
 */
export const createCustomParams = (isLongtime = true) => {
    const customParams = {
        region: createRegionOptions().map(item => item.value),
        logStore: createLogStoreOptions().filter(item => item.isLongtime === isLongtime).map(item => item.value),
    };
    return customParams;
};

/**
* 创建日志仓库名称
* <AUTHOR>
* @date 2023-08-08
* @param {Boolean} isLongtime
* @returns {String}
*/
export const createLogStore = (isLongtime = true): string => createLogStoreOptions().find(item => item.isLongtime === isLongtime)?.value || '';

/**
 * 解析日志数据中的 message 字段，获取日志数据内容
 * <AUTHOR>
 * @date 2023-11-08
 * @param {String} message
 * @returns {Object}
 */
export const parseMessage = (message: string) => {
    let logData = null;
    try {
        const flag = 'content:';
        const index = message.indexOf(flag) + flag.length;
        const contentJson = message.slice(index);
        logData = JSON.parse(contentJson);
    } catch (error) {
        console.log('parseMessage error', error);
    }
    return logData;
};

/**
 * 解析日志数据中的 message 字段，获取日志数据内容
 * <AUTHOR>
 * @date 2023-11-08
 * @param {String} message
 * @returns {Object}
 */
export const findJSONObjects = (message: string) => {
    const check = (str: string) => {
        try {
            return JSON.parse(str);
        } catch (error) {
            return false;
        }
    };
    let sIndex = 0;
    let startIndex = -1;
    let eIndex = 0;
    const isContinue = true;
    const objects = <any []>[];
    do {
        if (startIndex === -1) {
            startIndex = message.indexOf('{', sIndex);
            if (startIndex === -1) {
                break;
            }
            eIndex = startIndex;
        }
        let endIndex = message.indexOf('}', eIndex);
        if (endIndex === -1) {
            break;
        }
        endIndex += 1;
        const str = message.slice(startIndex, endIndex);
        const obj = check(str);
        if (!obj) {
            eIndex = endIndex;
            continue;
        }
        objects.push(obj);
        startIndex = -1;
        sIndex = endIndex;
    } while (isContinue);
    return objects;
};

/**
 * 解析日志数据中的 clinicId 字段，获取门店ID
 * <AUTHOR>
 * @date 2023-11-08
 * @param {String} message
 * @returns {Object}
 */
export const parseClinicId = (message: string) => {
    let clinicId = null;
    try {
        // clinicID肯定32个长度
        clinicId = message.slice(9, 41);
    } catch (error) {
        console.log('parseMessage error', error);
    }
    return clinicId;
};

/**
 * 通过eval执行代码，返回执行结果
 * <AUTHOR>
 * @date 2023-11-20
 * @param {String} code
 * @returns {any}
 */
export const eval2 = (code: string) => {
    const Fun = Function;
    return new Fun(`return ${code}`)();
};

/**
 * 检查是否匹配
 * <AUTHOR>
 * @date 2023-11-20
 * @param {Object} logItem
 * @param {Object} classifyInfo
 * @returns {Boolean}
 */
export const checkIsMate = (logItem: any, classifyInfo: any) => {
    let isMate = false;
    try {
        const includes = classifyInfo?.includes || [];
        const checkCode = classifyInfo?.checkCode || '';
        const response = logItem.data.response;
        const requestData = (() => {
            const {
                requestData,
                options,
            } = logItem.data;
            if (requestData) {
                return requestData;
            }
            if (options?.body) {
                return options.body;
            }
            if (options?.requestData) {
                return options.requestData;
            }
            return logItem.data;
        })();
        const message = response.message;
        if (checkCode) {
            // 通过自定义函数处理消息转义
            isMate = !!eval2(checkCode)(message, requestData, response);
        } if (includes.length !== 0) {
            // 通过关键词搜索，都存在则视为匹配
            isMate = includes.every((one: string) => message.indexOf(one) !== -1);
        }
    } catch (error) {
        //
    }
    return isMate;
};

/**
 * 获取区域展示信息，通过区域代码获取区域名称，如果没有则返回空字符串
 * <AUTHOR>
 * @date 2023-11-21
 * @param {String} region
 * @returns {String}
 */
export const getRegionWording = (region: string) => {
    const target = regionNameOptions.find((item: any) => item.region === region);
    return target ? `${target.provinceShortName}-${target.regionShortName}(${region})` : region;
};

/**
 * 获取区域名称，通过区域代码获取区域名称，如果没有则返回空字符串
 * <AUTHOR>
 * @date 2025-07-29
 * @param {String} region
 * @returns {String}
 */
export const getRegionNameWording = (region: string) => {
    const target = regionNameOptions.find((item: any) => item.region === region);
    return target ? `${target.provinceShortName}-${target.regionShortName}` : region;
};

/**
 * 按层级分组
 * <AUTHOR>
 * @date 2025-06-10
 * @returns {Array}
 */
export const getRegionCascaderOptions = () => {
    const cascaderOptions = [
        // {
        //     name: '北京',
        //     check: (region: string) => region === 'beijing',
        // },
        {
            name: '天',
            check: (region: string) => region === 'tianjin',
        },
        {
            name: '河北',
            check: (region: string) => region.startsWith('hebei_'),
        },
        {
            name: '山西',
            check: (region: string) => region.startsWith('mountainwest_'),
        },
        {
            name: '内蒙古',
            check: (region: string) => region.startsWith('mountaineast_'),
        },
        {
            name: '辽宁',
            check: (region: string) => region.startsWith('liaoning_'),
        },
        {
            name: '吉林',
            check: (region: string) => region.startsWith('jilin_'),
        },
        {
            name: '黑龙江',
            check: (region: string) => region.startsWith('heilongjiang_'),
        },
        // {
        //     name: '上海',
        //     check: (region: string) => region === 'shanghai',
        // },
        {
            name: '江苏',
            check: (region: string) => region.startsWith('jiangsu_'),
        },
        {
            name: '浙江',
            check: (region: string) => region.startsWith('zhejiang_'),
        },
        {
            name: '安徽',
            check: (region: string) => region.startsWith('anhui_'),
        },
        {
            name: '福建',
            check: (region: string) => region.startsWith('fujian_'),
        },
        {
            name: '江西',
            check: (region: string) => region.startsWith('jiangxi_'),
        },
        {
            name: '山东',
            check: (region: string) => region.startsWith('shandong_'),
        },
        {
            name: '河南',
            check: (region: string) => region.startsWith('henan_'),
        },
        {
            name: '湖北',
            check: (region: string) => region.startsWith('hubei_'),
        },
        {
            name: '湖南',
            check: (region: string) => region.startsWith('hunan_'),
        },
        {
            name: '广东',
            check: (region: string) => region.startsWith('guangdong_') || region.startsWith('guangzhou_') || region.startsWith('shenzhen_'),
        },
        {
            name: '广西',
            check: (region: string) => region.startsWith('guangxi_'),
        },
        {
            name: '海南',
            check: (region: string) => region.startsWith('hainan_'),
        },
        {
            name: '重庆',
            check: (region: string) => region.startsWith('chongqing_'),
        },
        {
            name: '四川',
            check: (region: string) => region.startsWith('sichuan_'),
        },
        {
            name: '贵州',
            check: (region: string) => region.startsWith('guizhou_'),
        },
        {
            name: '云南',
            check: (region: string) => region.startsWith('yunnan_'),
        },
        {
            name: '西藏',
            check: (region: string) => region.startsWith('xizang_'),
        },
        {
            name: '陕西',
            check: (region: string) => region.startsWith('shanxi_'),
        },
        {
            name: '甘肃',
            check: (region: string) => region.startsWith('gansu_'),
        },
        // {
        //     name: '青海',
        //     check: (region: string) => region.startsWith('qinghai_'),
        // },
        {
            name: '宁夏',
            check: (region: string) => region.startsWith('ningxia_'),
        },
        {
            name: '新疆',
            check: (region: string) => region.startsWith('xinjiang_'),
        },
    ];
    return cascaderOptions.map((item, index) => {
        const itemInfo = {
            value: index,
            label: item.name,
            children: regionNameOptions
                            .filter((one) => item.check(one.region))
                            .map((one) => ({
                                value: one.region,
                                label: one.regionShortName,
                            })),
        };
        if (itemInfo.children.length === 0) {
            return null;
        }
        if (itemInfo.children.length === 1) {
            return itemInfo.children[0];
        }
        return itemInfo;
    }).filter((item) => item !== null);
};

/**
 * 创建日期格式化函数，返回格式化后的日期字符串，格式为：YYYY-MM-DD HH:mm:ss
 * <AUTHOR>
 * @date 2023-11-22
 * @param {any} date
 * @returns {String}
 */
export const createDateTimeFormat19 = (date?: any) => dayjs(date).format('YYYY-MM-DD HH:mm:ss');

/**
 * 创建日期格式化函数，返回格式化后的日期字符串，格式为：YYYY-MM-DD
 * <AUTHOR>
 * @date 2023-11-22
 * @param {any} date
 * @returns {String}
 */
export const createDateFormat = (date?: any) => dayjs(date).format('YYYY-MM-DD');

/**
 * 创建日期格式化函数，返回格式化后的日期字符串，格式为：YYYY-MM
 * <AUTHOR>
 * @date 2023-11-22
 * @param {any} date
 * @returns {String}
 */
export const createMonthFormat = (date?: any) => dayjs(date).format('YYYY-MM');

/**
 * 创建日期格式化函数，返回格式化后的日期字符串，格式为：YYYYMM
 * <AUTHOR>
 * @date 2023-11-22
 * @param {any} date
 * @returns {String}
 */
export const createMonthFormat6 = (date?: any) => dayjs(date).format('YYYYMM');

/**
 * 将params数据，按照target的结构赋值，不改变target结构
 * <AUTHOR>
 * @date 2021-04-23
 * @param {Object} target 目标结构
 * @param {Object} params 入参数据，需要与目标一样数据结构，这样才能赋值
 * @param {Boolean} isDeep 是否深度赋值
 * @returns {Object}
 */
export const pick = (target: any, params: any, isDeep = true) => {
    if (!isEmpty(target) && !isEmpty(params)) {
        for (const key in target) {
            const value = target[key];
            if (isDeep && Array.isArray(value) && isObject(value[0])) {
                // 数组
                const dataList = params[key] || [];
                target[key] = dataList.map((item: any) => {
                    const template = cloneDeep(value[0]);
                    return pick(template, item, isDeep);
                });
            } else if (isDeep && isObject(value)) {
                // 对象
                target[key] = pick(value, params[key], isDeep);
            } else {
                // 键值
                target[key] = !isEmpty(params[key]) ? params[key] : target[key];
            }
        }
    }
    return target;
};

/**
 * 加载js代码
 * <AUTHOR>
 * @date 2023-08-22
 * @param {String} url
 * @param {String} id
 * @returns {Promise<Response>}
 */
export const loadScript = (url: string, id: string) => new Promise((resolve) => {
    const head = document.getElementsByTagName('head')[0];
    const script = document.createElement('script');
    script.type = 'text/javascript';
    if (id) {
        script.id = id;
    }
    // 其他浏览器
    script.onload = function () {
        resolve(AbcResponse.success());
    };
    script.defer = true;
    script.src = url;
    head.appendChild(script);
});

export const getTargetWording = (options: any, value: string | number, defaultValue: string | number = '') => {
    const target = options.find((item: any) => item.value === value);
    return target?.label || target?.name || defaultValue;
};

/**
 * 将对象转成数组
 * <AUTHOR>
 * @date 2023-05-25
 * @param {Object} obj 目标对象
 * @param {Array} keys 前缀数组
 * @returns {Array}
 */
export const objToArr = (obj: any, keys: any = []) => {
    const arr = <any []>[];
    const addItem = (keys: any, value: any) => {
        const item = {
            key: keys.join('.'),
            value,
        };
        pushItems(item);
    };
    const pushItems = (...items: any[]) => {
        for (let index = 0; index < items.length; index++) {
            const item = items[index];
            const isExist = arr.find((one) => one.key === item.key);
            if (isExist) {
                continue;
            }
            arr.push(item);
        }
    };
    Object.keys(obj || {}).forEach((key) => {
        const value = obj[key];
        if (Array.isArray(value)) {
            const keys1 = [...keys, key, '[n]'];
            value.forEach((item) => {
                if (isObject(item)) {
                    const arrSub = objToArr(item, keys1);
                    pushItems(...arrSub);
                } else {
                    addItem(keys1, item);
                }
            });
        } else if (isObject(value)) {
            const keys1 = [...keys, key];
            const arrSub = objToArr(value, keys1);
            pushItems(...arrSub);
        } else {
            const keys1 = [...keys, key];
            addItem(keys1, value);
        }
    });
    return arr;
};

/**
 * 上传json数据到oss
 * <AUTHOR>
 * @date 2024-05-08
 * @param {Object} data
 * @param {String} path
 * @returns {Promise<AbcResponse>}
 */
// export const uploadJsonDataToOss = async (data: any, fileName: string) => {
//     let uploadResponse: any = null;
//     try {
//         const blob = new Blob([JSON.stringify(data)], { type: 'text/json' });
//         const jsonFile = new File([blob], 'temp.json', {
//             type: 'text/json',
//         });
//         let { url } = await OSSUtil.upload({
//             bucket: import.meta.env.VITE_APP_OSS_BUCKET,
//             region: import.meta.env.VITE_APP_OSS_REGION,
//             rootDir: 'oa/protocol',
//             fileName,
//         }, jsonFile);
//         url = url.replace('cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com', 'assets-dev.abczs.cn');
//         url = url.replace('cd-cis-static-assets-test.oss-cn-chengdu.aliyuncs.com', 'assets-test.abczs.cn');
//         url = url.replace('cd-cis-static-assets.oss-cn-chengdu.aliyuncs.com', 'assets.abcyun.cn');
//         uploadResponse = AbcResponse.success({ url });
//     } catch (error) {
//         uploadResponse = AbcResponse.error('上传失败');
//     }
//     return uploadResponse;
// };

/**
 * 值转多选数组
 * <AUTHOR>
 * @date 2024-06-19
 * @param {Number} value
 * @param {Array} options
 * @returns {Array}
 */
export const valueToMultipleArray = (value: number, options: any) => {
    let _value = Number(value || 0);
    return options
                    .slice()
                    .sort((a: any, b: any) => b.value - a.value)
                    .reduce((arr: any, item: any) => {
                        if (item.value <= _value) {
                            arr.push(item.value);
                            _value -= item.value;
                        }
                        return arr;
                    }, []);
};

/**
 * 多选数组转值
 * <AUTHOR>
 * @date 2024-06-19
 * @param {Array} multipleArray
 * @returns {Number}
 */
export const multipleArrayToValue = (multipleArray: any) => multipleArray.reduce((value: number, item: number) => {
    value += item;
    return value;
}, 0);

/**
 * 执行函数的超时包装
 * <AUTHOR>
 * @date 2024-09-10
 * @param {Promise} promise
 * @param {Number} timeout
 * @returns {Promise}
 */
export const runTimeoutPack = async (promise: any, timeout = 1000 * 20) => await Promise.race([
    promise,
    (async () => {
        await delayPromise(timeout);
        return AbcResponse.error('等待响应超时');
    })(), // 20秒超时
]);

/**
 * 远程执行js
 * <AUTHOR>
 * @date 2024-06-19
 * @param {String} deviceCode
 * @param {String} js
 * @returns {String}
 */
export const remoteRunJs = async (deviceCode: string, js: string) => {
    let runResponse: any = null;
    try {
        const code = `
            window.remoteMain = async () => {
                let response = null
                try {
                    ${js}
                } catch (error) {
                    response = { status: false, message: error.message }
                }
                return JSON.stringify(response)
            }
            window.remoteMain()
        `;
        const result = await window.remoteSDK.shellExecutor.executeJavaScript(deviceCode, code);
        runResponse = JSON.parse(result.data);
    } catch (error: any) {
        runResponse = AbcResponse.error(error.message || '执行失败');
    }
    console.log('runResponse', runResponse);
    return runResponse;
};

/**
 * 远程执行shell
 * <AUTHOR>
 * @date 2024-06-19
 * @param {String} deviceCode
 * @param {String} shell
 * @returns {String}
 */
export const remoteRunShell = async (deviceCode: string, shell: string) => {
    let runResponse: any = null;
    try {
        const result = await window.remoteSDK.shellExecutor.exeShellScripts(deviceCode, shell);
        if (result?.exitCode !== 0) {
            throw new Error(result?.errorData);
        }
        runResponse = AbcResponse.success({ result: result.data });
    } catch (error: any) {
        runResponse = AbcResponse.error(error.message || '执行失败');
    }
    console.log('runResponse', runResponse);
    return runResponse;
};

/**
 * 加载social资源
 * <AUTHOR>
 * @date 2024-03-20
 * @returns {Promise<AbcResponse>}
 */
let inited = false; // 是否初始化
export const loadSocial = async () => {
    if (inited) {
        return AbcResponse.success();
    }
    const loadPromise = new Promise((resolve: any) => {
        window.loadSocialSuccess = () => resolve();
    });
    window.$platform = {
        context: {
            store: {
                getters: {
                    currentRegion: 'xxx',
                },
                state: {},
            },
        },
        installModule: () => {},
    };
    // 开发  https://static-dev-cdn.abczs.cn
    // 预发  https://static-pre-cdn.abcyun.cn
    // 灰度  https://static-gray-cdn.abcyun.cn
    // 正式  https://cis-static-prod.oss-cn-shanghai.aliyuncs.com
    const domain = isProd ? 'https://static-pre-cdn.abcyun.cn' : 'https://static-dev-cdn.abczs.cn';
    const js = `${domain}/abc-micro-frontend/social/module-loader.js`;
    await loadScript(`${js}?t=${Date.now()}`, '');
    await loadPromise;
    inited = true;
    return AbcResponse.success();
};

/**
 * 打开一个下载页面
 * <AUTHOR>
 * @date 2024-08-15
 * @param {String} url
 */
export const openDownloadPage = (url: string) => {
    const a = document.createElement('a');
    a.href = url;
    a.target = '_blank';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
};

/**
 * 请求社保信息
 * <AUTHOR>
 * @date 2024-08-14
 * @param {String} deviceCode
 * @returns {Promise<AbcResponse>}
 */
export const requestSocialInfo = async (deviceCode: string) => {
    if (!deviceCode) {
        return AbcResponse.error('缺少deviceCode参数');
    }
    const js = `
        if (!window.$national) {
            throw new Error('功能不可用：当前门店没有region，或地区医保未对接')
        }
        response = {
            status: true,
            message: '',
            data: {
                isOpenSocial: window.$national.vuexGetters('isOpenSocial'),
                clinicId: window.$national.vuexState('clinicId'),
                basicInfo: window.$national.vuexState('basicInfo'),
                isHospital: window.$national.vuexGetters('isHospital'),
                isPharmacy: window.$national.vuexGetters('isPharmacy'),
                regionPrincipal: window.$national.config.regionPrincipal,
                isSocialMode: window.$national.isSocialMode,
                isAloneMode: window.$national.isAloneMode,
                region: window.$national.region,
                provinceName: window.$national.config.provinceName,
                regionName: window.$national.config.regionName,
                setlOptins: window.$national.config.setlOptins,
                isNational: window.$national.config.isNational,
                apiVersion: window.$national.config.apiVersion,
                isProxyServers: window.$national.isProxyServers,
                isPrivateNetworkMode: window.$national.isPrivateNetworkMode,
                isBluetoothMode: window.$national.isBluetoothMode,
                isMac: window.$national.isMac,
                isWindowsElectron: window.$national.isWindowsElectron,
                isSocialCom: window.$national.isSocialCom,
            }
        }
    `;
    const response = await remoteRunJs(deviceCode, js);
    if (response.status === false) {
        return response;
    }
    return response;
};

/**
 * 请求加载社保插件
 * <AUTHOR>
 * @date 2024-08-14
 * @param {String} region
 * @returns {Promise<AbcResponse>}
 */
export const requestLoadSocial = async (region: string) => {
    if (!region) {
        return AbcResponse.error('缺少region参数');
    }
    window.$national = null;
    const loadResponse = await loadSocial();
    if (loadResponse.status === false) {
        return loadResponse;
    }
    const mode = 'social'; // 完整模式
    const nationalInstall = window.createNationalInstall(mode, region);
    await nationalInstall.init();
    window.$platform.context.store = nationalInstall.createStore();
    window.$national = nationalInstall.$national;
    return AbcResponse.success({ $national: window.$national });
};

/**
 * 下载文件
 * <AUTHOR>
 * @date 2024-10-09
 * @param {String} fileName
 * @param {String} fileContent
 */
export const downloadFile = (fileName: string, fileContent: string) => {
    const blob = new Blob([fileContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();

    document.body.removeChild(a);
    URL.revokeObjectURL(url);
};

/**
 * 下载文件
 * <AUTHOR>
 * @date 2024-10-09
 * @param {Array} logItems
 * @param {Number} limit
 */
export const logItemsLimit = (logItems: any, limit: number) => {
    const dataList = logItems
                    .slice()
                    .sort((a: any, b: any) => {
                        try {
                            const aTime = new Date(a.time.slice(0, 19)).getTime();
                            const bTime = new Date(b.time.slice(0, 19)).getTime();
                            return aTime > bTime ? -1 : 1;
                        } catch (error) {
                            return -1;
                        }
                    });
    return dataList.slice(0, limit);
};

/**
 * 检查是否显示
 * <AUTHOR>
 * @date 2024-06-14
 * @param {Object} value
 * @param {Object} params
 * @returns {Boolean}
 */
export function checkIsShow(value: any, params = {}) {
    if (typeof value === 'function') {
        return !!value(params);
    }
    if (isEmpty(value)) {
        return true;
    }
    return !!value;
}

/**
 * 过滤选项，通过show字段
 * <AUTHOR>
 * @date 2024-06-14
 * @param {Array} configList
 * @param {Object} params
 * @param {String} key
 * @returns {Array}
 */
export function filterConfigList(configList: any, params = {}, key = 'show') {
    return (configList || []).filter((item: any) => {
        const value = item[key];
        return checkIsShow(value, params);
    });
}

/**
 * 拷贝
 * <AUTHOR>
 * @date 2025-01-11
 * @param {any} value:string
 */
export const copy = (value: string) => {
    // 动态创建 textarea 标签
    const textarea: any = document.createElement('textarea');
    // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
    textarea.readOnly = 'readonly';
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    // 将要 copy 的值赋给 textarea 标签的 value 属性
    // 网上有些例子是赋值给innerText,这样也会赋值成功，但是识别不了\r\n的换行符，赋值给value属性就可以
    textarea.value = value;
    // 将 textarea 插入到 body 中
    document.body.appendChild(textarea);
    // 选中值并复制
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    document.execCommand('Copy');
    document.body.removeChild(textarea);
};

/**
 * 日期快捷选择
 * <AUTHOR>
 * @date 2025-01-11
 * @returns {Array}
 */
export const createShortcuts = () => ([
    {
        text: '今天',
        value: () => {
            const date = dayjs();
            const s = date.startOf('day').toDate();
            const e = date.endOf('day').toDate();
            return [s, e];
        },
    },
    {
        text: '昨天',
        value: () => {
            const date = dayjs().subtract(1, 'day');
            const s = date.startOf('day').toDate();
            const e = date.endOf('day').toDate();
            return [s, e];
        },
    },
    {
        text: '本周',
        value: () => {
            const date = dayjs();
            const s = date.startOf('week').add(1, 'day').toDate();
            const e = date.endOf('week').add(1, 'day').toDate();
            return [s, e];
        },
    },
    {
        text: '上周',
        value: () => {
            const date = dayjs().subtract(1, 'week');
            const s = date.startOf('week').add(1, 'day').toDate();
            const e = date.endOf('week').add(1, 'day').toDate();
            return [s, e];
        },
    },
    {
        text: '本月',
        value: () => {
            const date = dayjs();
            const s = date.startOf('month').toDate();
            const e = date.endOf('month').toDate();
            return [s, e];
        },
    },
    {
        text: '上月',
        value: () => {
            const date = dayjs().subtract(1, 'month');
            const s = date.startOf('month').toDate();
            const e = date.endOf('month').toDate();
            return [s, e];
        },
    },
    {
        text: '今年',
        value: () => {
            const date = dayjs();
            const s = date.startOf('year').toDate();
            const e = date.endOf('year').toDate();
            return [s, e];
        },
    },
    {
        text: '去年',
        value: () => {
            const date = dayjs().subtract(1, 'year');
            const s = date.startOf('year').toDate();
            const e = date.endOf('year').toDate();
            return [s, e];
        },
    },
]);

/**
 * 向上取整
 * <AUTHOR>
 * @date 2025-07-31
 * @param {Number} num
 * @param {Number} len
 * @returns {Number}
 */
export const roundUpToZero = (num: number, len = 1) => {
    const a = Math.pow(10, len);
    return Math.ceil(num / a) * a;
}

/**
 * 手动实现单选
 * <AUTHOR>
 * @date 2025-09-09
 * @param selectList
 */
export const cascaderSelectSingle = (selectList: string[]) => {
    let laseSelect = ''
    const result = []
    // 只保留最后一次选择的第一级
    for (const select of selectList) {
        if (!laseSelect || laseSelect !== select[0]) {
            // 有新的进来
            laseSelect = select[0]
            result.length = 0
        }
        result.push(select)
    }
    return result
}
