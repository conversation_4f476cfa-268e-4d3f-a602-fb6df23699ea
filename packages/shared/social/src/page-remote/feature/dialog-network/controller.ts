/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { computed, reactive } from 'vue';
import * as utils from '../../../common/utils';

import createLoadingModel from '../../../model/loading';

export const createNetwordController = (props: any) => {
    const loadingModelAll = createLoadingModel();
    const loadingModelTest = createLoadingModel();

    const networkList = reactive(<any []>[]);

    const formData = reactive({
        host: '', // 地址
        port: '', // 端口
        action: '', // 方式
    });

    // 是否禁用查询按钮
    const isDisabledTest = computed(() => {
        if (formData.action === 'ping') {
            return !(
                formData.host
            );
        } 
        return !(
            formData.host
                && formData.port
        );
    });

    /**
     * 初始化常用地址
     * <AUTHOR>
     * @date 2024-08-15
     * @param {Array} list
     */
    const initNetworkList = (list: any) => {
        list.forEach((item: any) => {
            const itemInfo = {
                name: item.name || '', // 名称
                host: item.host || item.ip || '', // 地址
                port: item.port || (item.protocol === 'https' ? 443 : 80), // 端口
                tips: item.tips || '', // 提示信息，描述地址不通影响哪些功能的使用
                test: false, // 是否在检测
                telnet: false, // 是否在telnet
                result: 0, // 结果，0 默认状态；1 通；2 不通；3 失败
                resultTips: '',
            };
            if (itemInfo.host) {
                networkList.push(itemInfo);
            }
        });
    };
    const networkConfigList: any = window.$national.options.networkConfigList || [];
    initNetworkList(networkConfigList);
    initNetworkList(networkConfigList);

    /**
     * 初始化表单数据
     * <AUTHOR>
     * @date 2024-08-15
     */
    const initFormData = () => {
        Object.assign(formData, {
            host: '', // 地址
            port: 80, // 端口
            action: 'ping', // 方式
        });
    };

    /**
     * 请求ping
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} item
     * @returns {Promise<AbcResponse>}
     */
    const requestPing = async (item: any) => {
        const js = `
            const host = '${item.host}'
            response = await window.$national.electron.ping(host)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求ping
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} item
     * @returns {Promise<AbcResponse>}
     */
    const requestTelnet = async (item: any) => {
        const js = `
            const host = '${item.host}'
            const port = ${item.port}
            response = await window.$national.electron.telnet(host, port)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        loadingModelAll,
        loadingModelTest,
        networkList,
        formData,
        isDisabledTest,
        initFormData,
        requestPing,
        requestTelnet,
    };
};