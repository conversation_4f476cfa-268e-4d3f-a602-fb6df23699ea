/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { computed, reactive } from 'vue';
import * as utils from '../../../common/utils';

import createPageModel from '../../../model/page';
import createLoadingModel from '../../../model/loading';

export const createLimitPriceController = (props: any) => {
    const loadingModelQuery = createLoadingModel();
    const pageModel = createPageModel();

    const formData = reactive({
        queryDate: '', // 查询时间点
        hilistCode: '', // 医保目录编码
        hilistLmtpricType: '', // 医保目录限价类型
        overlmtDspoWay: '', // 医保目录超限处理方式
        insuAdmdvs: '', // 参保机构医保区划
        begndate: '', // 开始日期
        enddate: '', // 结束日期
        valiFlag: '', // 有效标志
        id: '', // 唯一记录号
        tabname: '', // 表名
        poolareaNo: '', // 统筹区
        updtTime: '', // 更新时间
        pageNum: 1, // 当前页数
        pageSize: 10, // 本页数据量
    });

    const queryResponse = reactive({
        originData: <any> null,
    });

    // 总条数
    const total = computed(() => queryResponse.originData?.output?.recordCounts || 0);

    // 是否禁用查询按钮
    const isDisabledQueryBtn = computed(() => !(
        formData.updtTime
    ));

    // 医保目录限价类型选项
    const hilistLmtprcTypeOptions = computed(() => window.$national.options.hilistLmtprcTypeOptions);

    // 医保目录超限处理方式选项
    const overLmtDspoWayOptions = computed(() => window.$national.options.overLmtDspoWayOptions);

    // 有效标识选项
    const valiFlagOptions = computed(() => window.$national.options.valiFlagOptions);

    // 展示数据
    const showDataList = computed(() => (queryResponse.originData?.output?.data || []).map((item: any) => {
        const itemInfo = {
            ...item,
        };
        itemInfo.hilistLmtpricTypeWording = window.$national.tools.getHilistLmtpricType(item.hilist_lmtpric_type); // 医保目录限价类型
        itemInfo.overlmtDspoWayWording = window.$national.tools.getOverLmtDspoWayWording(item.overlmt_dspo_way); // 医保目录超限处理方式
        itemInfo.begndateWording = item.begndate ? window.$national.tools.getDateFormat(item.begndate) : ''; // 开始日期
        itemInfo.enddateWording = item.enddate ? window.$national.tools.getDateFormat(item.enddate) : ''; // 结束日期
        itemInfo.valiFlagWording = window.$national.tools.getValiFlagWording(item.vali_flag); // 有效标志
        return itemInfo;
    }));
    
    /**
     * 初始化表单数据
     * <AUTHOR>
     * @date 2024-08-14
     */
    const initFormData = () => {
        Object.assign(formData, {
            queryDate: '', // 查询时间点
            hilistCode: '', // 医保目录编码
            hilistLmtpricType: '', // 医保目录限价类型
            overlmtDspoWay: '', // 医保目录超限处理方式
            insuAdmdvs: '', // 参保机构医保区划
            begndate: '', // 开始日期
            enddate: '', // 结束日期
            valiFlag: valiFlagOptions.value[1].value, // 有效标志
            id: '', // 唯一记录号
            tabname: '', // 表名
            poolareaNo: '', // 统筹区
            updtTime: '2020-01-01', // 更新日期
            pageNum: 1, // 当前页数
            pageSize: 10, // 本页数据量
        });
    };

    /**
     * 创建查询参数
     * <AUTHOR>
     * @date 2024-08-06
     * @returns {Object}
     */
    const createParams = () => {
        const {
            queryDate, // 查询时间点
            hilistCode, // 医保目录编码
            hilistLmtpricType, // 医保目录限价类型
            overlmtDspoWay, // 医保目录超限处理方式
            insuAdmdvs, // 参保机构医保区划
            begndate, // 开始日期
            enddate, // 结束日期
            valiFlag, // 有效标志
            id, // 唯一记录号
            tabname, // 表名
            poolareaNo, // 统筹区
            updtTime, // 更新日期
            pageNum, // 当前页数
            pageSize, // 本页数据量
        } = formData;
        const params = {
            data: {
                query_date: queryDate ? window.$national.tools.getDateFormat(queryDate) : '', // 查询时间点
                hilist_code: hilistCode, // 医保目录编码
                hilist_lmtpric_type: hilistLmtpricType, // 医保目录限价类型
                overlmt_dspoWay: overlmtDspoWay, // 医保目录超限处理方式
                insu_admdvs: insuAdmdvs, // 参保机构医保区划
                begndate: begndate ? window.$national.tools.getDateFormat(begndate) : '', // 开始日期
                enddate: enddate ? window.$national.tools.getDateFormat(enddate) : '', // 结束日期
                vali_flag: valiFlag, // 有效标志
                id, // 唯一记录号
                tabname, // 表名
                poolareaNo, // 统筹区
                updt_time: window.$national.tools.getDateFormat(updtTime), // 更新时间
                page_num: pageNum, // 当前页数
                page_size: pageSize, // 本页数据量
            },
        };
        return params;
    };

    /**
     * 请求字典查询
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestLimitPrice = async (params: any) => {
        const js = `
            const params = ${JSON.stringify(params)}
            response = await window.$national.protocol.NationalSocialSecurity.instance.limitPriceDirectory(params)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        loadingModelQuery,
        pageModel,
        formData,
        queryResponse,
        total,
        isDisabledQueryBtn,
        hilistLmtprcTypeOptions,
        overLmtDspoWayOptions,
        valiFlagOptions,
        showDataList,
        initFormData,
        createParams,
        requestLimitPrice,
    };
};