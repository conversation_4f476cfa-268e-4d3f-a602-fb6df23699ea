<template>
    <el-dialog
        v-model="isShowDialogLimitPrice"
        title="限价目录查询"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-limit-price"
    >
        <el-form
            :inline="true"
            :model="formData"
            label-position="top"
            class="query-wrapper"
        >
            <el-form-item label="查询时间点">
                <el-date-picker v-model="formData.queryDate" type="date" clearable></el-date-picker>
            </el-form-item>
            <el-form-item label="医保目录编码">
                <el-input v-model="formData.hilistCode" clearable></el-input>
            </el-form-item>
            <el-form-item label="医保目录限价类型">
                <el-select v-model="formData.hilistLmtpricType" clearable>
                    <el-option
                        v-for="item in hilistLmtprcTypeOptions"
                        :key="item.value"
                        :label="item.name"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="医保目录超限处理方式">
                <el-select v-model="formData.overlmtDspoWay" clearable>
                    <el-option
                        v-for="item in overLmtDspoWayOptions"
                        :key="item.value"
                        :label="item.name"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="参保机构医保区划">
                <el-input v-model="formData.insuAdmdvs" clearable></el-input>
            </el-form-item>
            <el-form-item label="开始日期">
                <el-date-picker v-model="formData.begndate" type="date" clearable></el-date-picker>
            </el-form-item>
            <el-form-item label="结束日期">
                <el-date-picker v-model="formData.enddate" type="date" clearable></el-date-picker>
            </el-form-item>
            <el-form-item label="有效标志">
                <el-select v-model="formData.valiFlag" clearable>
                    <el-option
                        v-for="item in valiFlagOptions"
                        :key="item.value"
                        :label="item.name"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="唯一记录号">
                <el-input v-model="formData.id" clearable></el-input>
            </el-form-item>
            <el-form-item label="统筹区">
                <el-input v-model="formData.poolareaNo" clearable></el-input>
            </el-form-item>
            <el-form-item label="更新时间">
                <el-date-picker v-model="formData.updtTime" type="date"></el-date-picker>
            </el-form-item>
            <el-form-item class="btn">
                <el-button
                    type="primary"
                    :disabled="isDisabledQueryBtn"
                    :loading="loadingModelQuery.loading.value"
                    @click="onClickQuery"
                >
                    查询
                </el-button>
            </el-form-item>
        </el-form>
        <el-table
            v-loading="loadingModelQuery.loading.value"
            :data="showDataList"
            :height="320"
            border
        >
            <el-table-column
                prop="hilist_code"
                label="医保目录编码"
                min-width="240"
                fixed="left"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="hilistLmtpricTypeWording"
                label="医保目录限价类型"
                min-width="200"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="overlmtDspoWayWording"
                label="医保目录超限处理方式"
                min-width="200"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="insu_admdvs"
                label="参保机构医保区划"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="begndateWording"
                label="开始日期"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="enddateWording"
                label="结束日期"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="hilist_pric_uplmt_amt"
                label="医保目录定价上限金额"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="valiFlagWording"
                label="有效标志"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="rid"
                label="唯一记录号"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="updt_time"
                label="更新时间"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="crter_id"
                label="创建人ID"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="crter_name"
                label="创建人姓名"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="crte_time"
                label="创建时间"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="crte_optins_no"
                label="创建机构"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="opter_id"
                label="经办人ID"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="opter_name"
                label="经办人姓名"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="opt_time"
                label="经办时间"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="optins_no"
                label="经办机构"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="poolarea_no"
                label="统筹区"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
        </el-table>
        <el-pagination
            v-model:current-page="formData.pageNum"
            v-model:page-size="formData.pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            background
            layout="sizes, total, ->, prev, pager, next"
            @size-change="onChangePageSize"
            @current-change="onChangePageNum"
        ></el-pagination>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { computed, onMounted } from 'vue';
import { createLimitPriceController } from './controller';

const props = defineProps({
    deviceCode: {
        type: String,
        default: '',
    },
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogLimitPrice = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModelQuery,
    formData,
    queryResponse,
    total,
    isDisabledQueryBtn,
    hilistLmtprcTypeOptions,
    overLmtDspoWayOptions,
    valiFlagOptions,
    showDataList,
    initFormData,
    createParams,
    requestLimitPrice,
} = createLimitPriceController(props);

onMounted(() => {
    initFormData();
});

/**
 * 当点击查询时
 * <AUTHOR>
 * @date 2024-08-07
 */
const onClickQuery = async () => {
    if (loadingModelQuery.loading.value) {
        return;
    }
    loadingModelQuery.setLoading(true);
    const params = createParams();
    const requestResponse = await requestLimitPrice(params);
    loadingModelQuery.setLoading(false);
    if (requestResponse.status === false) {
        return ElMessage.error('查询失败: ' + requestResponse.message);
    }
    queryResponse.originData = requestResponse.data;
};

/**
 * 当改变分页大小时
 * <AUTHOR>
 * @date 2024-08-08
 */
const onChangePageSize = () => {
    formData.pageNum = 1;
    onClickQuery();
};
    
/**
 * 当改变分页页码时
 * <AUTHOR>
 * @date 2024-08-08
 */
const onChangePageNum = async () => {
    const pageNum = formData.pageNum;
    await onClickQuery();
    formData.pageNum = pageNum;
};
</script>

<style lang="scss">
    .social-module__feature__dialog-limit-price {
        width: 1200px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .query-wrapper {
            align-items: flex-end;

            .el-form-item {
                width: 156px;
                margin-right: 8px;
                margin-bottom: 8px;

                &:nth-child(7n) {
                    margin-right: 0;
                }
            }

            .type-item {
                width: 200px;

                .el-select {
                    width: 100%;
                }
            }

            .btn {
                width: 80px;
            }
        }

        .el-table {
            margin-top: 8px;
            margin-bottom: 16px;

            .el-table__empty-text {
                margin-top: 90px;
            }
        }
    }
</style>