<template>
    <div class="social-module__page-log-serve">
        <div class="tools-wrapper">
            <el-date-picker
                v-model="queryParams.dateRange"
                type="datetimerange"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="[
                    new Date(2000, 1, 1, 0, 0, 0),
                    new Date(2000, 1, 1, 23, 59, 59),
                ]"
                :shortcuts="shortcuts"
                :clearable="false"
                :width="300"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="截止时间"
            ></el-date-picker>
            <el-select
                v-model="queryParams.region"
                clearable
                multiple
                placeholder="分区筛选"
            >
                <el-option
                    v-for="item in regionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-select
                v-model="queryParams.logStore"
                clearable
                multiple
                placeholder="存储筛选"
            >
                <el-option
                    v-for="item in logStoreOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </div>
        <div class="search-wrapper">
            <el-autocomplete
                ref="autocompleteKeywordsRef"
                v-model="queryParams.keywords"
                :fetch-suggestions="onFetchSuggestion"
                placeholder="关键词 and 关键词 not 关键词 or 关键词"
                class="keywords"
                select-when-unmatched
                @select="onSelectSuggestionItem"
            ></el-autocomplete>
            <el-button
                type="primary"
                :icon="Search"
                @click="onClickSearchLog"
            >
                查询
            </el-button>
        </div>
        <div class="content-wrapper">
            <el-tabs
                v-model="queryParams.activeTab"
                type="border-card"
                class="tabs-wrapper"
            >
                <div v-if="queryParams.activeTab === 'timeline'" class="filter-wrapper">
                    <el-select
                        v-model="queryParams.topic"
                        clearable
                        multiple
                        collapse-tags
                        placeholder="topic筛选"
                        @change="onChangeTopic"
                    >
                        <el-option
                            v-for="item in topicOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </div>
                <el-tab-pane
                    v-loading="loadingModelSearch.loading.value"
                    label="日志集"
                    name="loglist"
                >
                    <el-scrollbar
                        v-if="searchResultDataList.length !== 0"
                        ref="logListRef"
                        v-infinite-scroll="onLoadMoreSearchResult"
                        class="search-result"
                    >
                        <div
                            v-for="item in searchResultDataList"
                            :key="item.id"
                            :class="{
                                'item-wrapper': true,
                                'is-selected': item.isSelected,
                            }"
                            @click="onClickSelectLogItem(item)"
                        >
                            <div class="one">
                                <span class="value"><span class="label">X-B3-SpanId:</span>{{ item['X-B3-SpanId'] }}</span>
                                <div class="track"></div>
                                <el-tag class="status" :type="item.levelType">
                                    {{ item.level }}
                                </el-tag>
                            </div>
                            <div class="one">
                                <span class="value"><span class="label">X-B3-TraceId:</span>{{ item['X-B3-TraceId'] }}</span>
                            </div>
                            <div class="one">
                                <span class="value ellipsis-1"><span class="label">location:</span>{{ item['location'] }}</span>
                            </div>
                            <div class="one">
                                <span
                                    v-if="item['message']"
                                    class="value ellipsis-6"
                                ><span class="label">message:</span>{{ item['message'] }}</span>
                                <span
                                    v-if="item['message-index']"
                                    class="value ellipsis-6"
                                ><span class="label">message-index:</span>{{ item['message-index'] }}</span>
                                <span
                                    v-if="item['message-no-index']"
                                    class="value ellipsis-6"
                                ><span class="label">message-no-index:</span>{{ item['message-no-index'] }}</span>
                            </div>
                            <div class="one">
                                <span class="value ellipsis-2"><span class="label">thread:</span>{{ item['thread'] }}</span>
                            </div>
                            <div class="one">
                                <span class="value ellipsis-2"><span class="label">__topic__:</span>{{ item['__topic__'] }}</span>
                            </div>
                            <div class="one">
                                <span class="value"><span class="label">time:</span>{{ item['time'] }}</span>
                                <div class="track"></div>
                                <el-button
                                    v-if="item['X-B3-TraceId']"
                                    :icon="ZoomIn"
                                    size="small"
                                    type="primary"
                                    plain
                                    @click.stop.prevent="onClickLookTimeline(item)"
                                >
                                    Trace
                                </el-button>
                            </div>
                        </div>
                    </el-scrollbar>
                    <div v-if="searchResultDataList.length === 0 && loadingModelSearch.loading.value === false" class="kong-wrapper">
                        <span>暂无日志</span>
                        <span v-if="!queryParams.keywords">请输入搜索关键词</span>
                    </div>
                </el-tab-pane>
                <el-tab-pane
                    v-loading="loadingModelSearch.loading.value"
                    label="Trace"
                    name="timeline"
                >
                    <el-scrollbar
                        v-if="timelineDataList.length !== 0"
                        ref="logTimelineRef"
                        v-infinite-scroll="onLoadMoreSearchResult"
                        class="log-timeline"
                    >
                        <el-timeline>
                            <el-timeline-item 
                                v-for="item in timelineDataList"
                                :key="item.id"
                                :timestamp="`${item.time} ${item.__topic__}`"
                                type="primary"
                                hollow
                                placement="top"
                            >
                                <div
                                    :class="{
                                        'item-wrapper': true,
                                        'is-selected': item.isSelected,
                                    }"
                                    @click="onClickSelectLogItem(item)"
                                >
                                    <div class="one">
                                        <span class="value"><span class="label">X-B3-SpanId:</span>{{ item['X-B3-SpanId'] }}</span>
                                        <div class="track"></div>
                                        <el-tag class="status" :type="item.levelType">
                                            {{ item.level }}
                                        </el-tag>
                                    </div>
                                    <div class="one">
                                        <span class="value"><span class="label">X-B3-TraceId:</span>{{ item['X-B3-TraceId'] }}</span>
                                    </div>
                                    <div class="one">
                                        <span class="value ellipsis-1"><span class="label">location:</span>{{ item['location'] }}</span>
                                    </div>
                                    <div class="one">
                                        <span
                                            v-if="item['message']"
                                            class="value ellipsis-6"
                                        ><span class="label">message:</span>{{ item['message'] }}</span>
                                        <span
                                            v-if="item['message-index']"
                                            class="value ellipsis-6"
                                        ><span class="label">message-index:</span>{{ item['message-index'] }}</span>
                                        <span
                                            v-if="item['message-no-index']"
                                            class="value ellipsis-6"
                                        ><span class="label">message-no-index:</span>{{ item['message-no-index'] }}</span>
                                    </div>
                                    <div class="one">
                                        <span class="value ellipsis-2"><span class="label">thread:</span>{{ item['thread'] }}</span>
                                    </div>
                                    <div class="one">
                                        <span class="value ellipsis-2"><span class="label">__topic__:</span>{{ item['__topic__'] }}</span>
                                    </div>
                                </div>
                            </el-timeline-item>
                        </el-timeline>
                    </el-scrollbar>
                    <div v-if="timelineDataList.length === 0 && loadingModelSearch.loading.value === false" class="kong-wrapper">
                        <span>暂无日志</span>
                        <span v-if="!queryParams.keywords">请输入搜索关键词</span>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <div class="total-wrapper">
                <span>{{ queryParams.activeTab === 'loglist' ? searchResultDataList.length : timelineDataList.length }} 条日志</span>
            </div>
            <div
                v-loading="loadingModelTarget.loading.value"
                class="detail-wrapper"
            >
                <template v-if="!!queryResponse.selectedItem">
                    <div class="header-wrapper">
                        <span class="tit">日志详情</span>
                        <span class="track"></span>
                        <el-button
                            v-if="targetLogInfo.clinicId"
                            type="primary"
                            plain
                            class="link-btn-wrapper"
                            @click="onClickLoginClinic"
                        >
                            登录门店
                        </el-button>
                        <el-button
                            v-if="targetLogInfo.clinicId"
                            type="primary"
                            plain
                            class="link-btn-wrapper"
                            @click="onClickRemoteExec"
                        >
                            远程执行
                        </el-button>
                        <el-button
                            type="primary"
                            plain
                            @click="onClickDownloadLog"
                        >
                            下载日志
                        </el-button>
                        <el-button
                            type="primary"
                            plain
                            @click="onClickCopyLog"
                        >
                            拷贝日志
                        </el-button>
                    </div>
                    <el-scrollbar
                        :key="targetLogInfo.originInfo?.logId"
                        class="json-wrapper"
                    >
                        <viewer-ctrl
                            v-model="num"
                            @full-screen="onClickFullScreen"
                        />
                        <vue-json-viewer
                            :key="num"
                            :value="targetLogInfo.originInfo"
                            :expand-depth="num"
                        ></vue-json-viewer>
                    </el-scrollbar>
                </template>
                <div v-else class="kong-wrapper">
                    <span>请选择日志查看详情</span>
                </div>
            </div>
        </div>

        <dialog-full-screen-viewer
            v-if="dialogModelFullScreenViewer.visible.value"
            v-model="dialogModelFullScreenViewer.visible.value"
            :num="num"
            :log-data="targetLogInfo.originInfo"
        />
    </div>
</template>

<script lang="ts" setup>
import VueJsonViewer from 'vue-json-viewer';
import ViewerCtrl from '../component/viewer-ctrl/index.vue';
import DialogFullScreenViewer from '../component/dialog-full-screen-viewer/index.vue';
import * as utils from '../common/utils';
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { quickLoginAbc } from '@abc-oa/utils/src/utils';
import { openExternal } from '@abc-oa/utils/src/electron';
import { Search, ZoomIn } from '@element-plus/icons-vue';
import { createPageRemoteController } from './controller';

const {
    dialogModelFullScreenViewer,
    loadingModelSearch,
    loadingModelTarget,
    shortcuts,
    queryParams,
    queryResponse,
    regionOptions,
    logStoreOptions,
    searchResultDataList,
    timelineDataList,
    topicOptions,
    targetLogInfo,
    setSearchRecord,
    getSearchRecord,
    createFileName,
    createFileContent,
    createLogSearchParams,
    createLogTimelineParams,
    requestLogSearchResult,
    requestLogTimeline,
    requestLoginClinicHref,
} = createPageRemoteController();

const router = useRouter();
const logListRef = ref();
const logTimelineRef = ref();
const autocompleteKeywordsRef = ref(null);
const num = ref(5);

onMounted(() => {
    // 通过路由传参，作为关键词搜索
    const {
        date,
        keywords,
    } = router.currentRoute.value.query || {};
    if (date) {
        queryParams.dateRange = [
            `${date} 00:00:00`,
            `${date} 23:59:59`,
        ];
    }
    if (keywords) {
        queryParams.keywords = keywords as string;
    }
    if (queryParams.keywords) {
        onClickSearchLog();
    }
});

/**
 * 当搜索建议时
 * <AUTHOR>
 * @date 2024-10-15
 * @param {String} keywords
 * @param {Function} callback
 */
const onFetchSuggestion = (keywords: string, callback: any) => {
    const searchRecord = getSearchRecord();
    const _keywords = keywords.trim();
    const dataList = searchRecord
                    // eslint-disable-next-line array-callback-return
                    .filter((item: string) => {
                        if (
                            _keywords === ''
                    || item.indexOf(_keywords) !== -1
                        ) {
                            return true;
                        }
                    })
                    .map((item: string) => ({
                        value: item,
                    }));
    callback(dataList);
};

/**
 * 当选择建议项时
 * <AUTHOR>
 * @date 2024-10-15
 */
const onSelectSuggestionItem = () => {
    autocompleteInputRef();
    onClickSearchLog();
};

/**
 * 自动补全输入框失焦
 * <AUTHOR>
 * @date 2024-10-15
 */
const autocompleteInputRef = () => {
    if (autocompleteKeywordsRef.value) {
        // @ts-ignore
        autocompleteKeywordsRef.value.close();
        // @ts-ignore
        autocompleteKeywordsRef.value.inputRef.blur();
    }
};

/**
 * 当点击搜索日志时
 * <AUTHOR>
 * @date 2024-08-06
 */
const onClickSearchLog = async () => {
    autocompleteInputRef();
    queryParams.activeTab = 'loglist';
    queryResponse.timelineData = null;
    loadingModelSearch.setLoading(true);
    const params = createLogSearchParams();
    const response = await requestLogSearchResult(params);
    loadingModelSearch.setLoading(false);
    if (!response || !utils.isEqual(params, createLogSearchParams())) {
        return response;
    }
    if (response.status === false) {
        ElMessage.error('拉取数据失败: ' + response.message);
        return response;
    }
    queryResponse.searchResult = response.data;
    if (logListRef.value) {
        logListRef.value?.setScrollTop(0);
    }
    if (searchResultDataList.value.length >= params.limit) {
        ElMessage.warning('日志条数大于100条，建议更精准的查询');
    }
    setSearchRecord(queryParams.keywords.trim());
    return queryResponse;
};

/**
 * 当加载更多搜索结果
 * <AUTHOR>
 * @date 2024-08-06
 */
const onLoadMoreSearchResult = async () => {
    // 暂时不用
};

/**
 * 当点击日志查看详情
 * <AUTHOR>
 * @date 2024-10-09
 * @param {Object} item
 */
const onClickSelectLogItem = (item: any) => {
    if (queryResponse.selectedItem?.id === item.id) {
        queryResponse.selectedItem = null;
        return;
    }
    queryResponse.selectedItem = item;
};

/**
 * 当点击日志查看时间线时
 * <AUTHOR>
 * @date 2024-10-09
 * @param {Object} item
 */
const onClickLookTimeline = async (item: any) => {
    queryParams.activeTab = 'timeline';
    loadingModelSearch.setLoading(true);
    const params = createLogTimelineParams(item);
    const response = await requestLogTimeline(params);
    loadingModelSearch.setLoading(false);
    if (!response || !utils.isEqual(params, createLogTimelineParams(item))) {
        return response;
    }
    if (response.status === false) {
        ElMessage.error('拉取数据失败: ' + response.message);
        return response;
    }
    queryParams.topic = [];
    queryResponse.timelineData = response.data;
    if (logTimelineRef.value) {
            logTimelineRef.value!.setScrollTop(0);
    }
    return queryResponse;
};

/**
 * 当改变topic时
 * <AUTHOR>
 * @date 2024-11-22
 */
const onChangeTopic = () => {
    if (logTimelineRef.value) {
        logTimelineRef.value?.setScrollTop(0);
    }
};

/**
 * 当点击登录门店
 * <AUTHOR>
 * @date 2025-01-03
 */
const onClickLoginClinic = async () => {
    const response = await requestLoginClinicHref(targetLogInfo.value.clinicId);
    if (response.status === false) {
        return ElMessage.error(response.message);
    }
    quickLoginAbc(response.data);
};

/**
 * 当点击远程执行
 * <AUTHOR>
 * @date 2025-01-03
 */
const onClickRemoteExec = async () => {
    const href = `${window.location.origin}/cs/social/remote?clinicId=${targetLogInfo.value.clinicId}`;
    openExternal(href);
};

/**
 * 当点击下载日志时
 * <AUTHOR>
 * @date 2024-10-09
 */
const onClickDownloadLog = () => {
    const fileName = createFileName();
    const fileContent = createFileContent();
    if (!fileContent) {
        return ElMessage.error('解析出入参失败，无法下载日志');
    }
    utils.downloadFile(fileName, fileContent);
};

/**
 * 当点击拷贝日志时
 * <AUTHOR>
 * @date 2024-10-09
 */
const onClickCopyLog = () => {
    const data = targetLogInfo.value.originInfo;
    utils.copy(JSON.stringify(data, null, 4));
    ElMessage.success('拷贝成功');
};

/**
 * 当点击全屏时
 * <AUTHOR>
 * @date 2025-05-30
 */
const onClickFullScreen = () => {
    dialogModelFullScreenViewer.show();
};
</script>

<style lang="scss">
    .social-module__page-log-serve {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        height: calc(100vh - 56px);
        background-color: #fff;
        padding: 16px;

        .tools-wrapper {
            display: flex;

            .el-date-editor {
                max-width: 330px;

                .el-icon {
                    margin-right: 4px;
                }

                .el-range-input {
                    width: 42%;
                }

                .el-range-separator {
                    max-width: 32px;
                    padding: 0;
                }

                .el-range__close-icon--hidden {
                    display: none;
                }
            }

            .el-select {
                margin-left: 8px;

                .el-input__wrapper {
                    height: 32px;
                }
            }
        }

        .search-wrapper {
            display: flex;
            margin-top: 8px;

            .keywords {
                flex: 1;
                margin-right: 8px;
            }
        }

        .content-wrapper {
            flex: 1;
            display: flex;
            margin-top: 12px;
            overflow: hidden;

            .tabs-wrapper {
                flex: 4;
                height: 100%;
                margin-right: 16px;
                border-radius: 4px;

                .el-tabs__header {
                    border-top-left-radius: 4px;
                    border-top-right-radius: 4px;
                    overflow: hidden;
                }

                .el-tabs__content {
                    position: relative;
                    padding: 0 0 0 15px !important;
                    height: calc(100% - 39px + 2px);
                    box-sizing: border-box;

                    .el-tab-pane {
                        height: 100%;
                    }
                }

                .filter-wrapper {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 38px;
                    padding: 0 8px;
                    box-sizing: border-box;
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    background-color: #f5f7fa;
                    border-bottom: 1px solid #e4e7ed;
                    z-index: 2001;

                    .el-select {
                        width: 280px;
                    }
                }

                .search-result,
                .log-timeline {
                    flex: 1;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;

                    .item-wrapper {
                        border-radius: 4px;
                        margin-top: 8px;
                        margin-right: 15px;
                        cursor: pointer;
                        border: 1px solid #dedfe0;
                        box-sizing: border-box;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        padding: 6px;

                        &:last-child {
                            margin-bottom: 8px;
                        }

                        div {
                            line-height: 18px;
                            margin: 2px 0;
                        }

                        .one {
                            display: flex;
                            justify-content: flex-start;

                            .label {
                                margin-right: 4px;
                                background-color: #edeff3;
                            }

                            .ellipsis-1 {
                                display: -webkit-box;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                word-break: break-all;
                                -webkit-box-orient: vertical;
                                -webkit-line-clamp: 1;
                            }

                            .ellipsis-2 {
                                @extend .ellipsis-1;

                                -webkit-line-clamp: 2;
                            }

                            .ellipsis-6 {
                                @extend .ellipsis-1;

                                -webkit-line-clamp: 6;
                            }

                            .track {
                                flex: 1;
                            }

                            .el-tag {
                                margin-left: 4px;
                            }
                        }

                        &:hover {
                            background: #ecf5ff;
                            border: 1px solid #d9ecff;
                        }

                        &.is-selected {
                            background: #d9ecff;
                            border: 1px solid #a0cfff;
                        }
                    }
                }

                .log-timeline {
                    margin-top: 48px;
                    padding-bottom: 30px;
                }

                .el-timeline-item {
                    transform: translateX(2px);
                }
            }

            .total-wrapper {
                width: 0;
                position: relative;
                top: 10px;
                left: -112px;

                > span {
                    color: #909399;
                    display: inline-block;
                    width: 80px;
                    text-align: right;
                }
            }

            .detail-wrapper {
                flex: 3;
                height: 100%;
                display: flex;
                flex-direction: column;

                .header-wrapper {
                    height: 32px;
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;

                    .tit {
                        color: #303133;
                        font-size: 15px;
                        font-weight: 700;
                    }

                    .track {
                        flex: 1;
                    }
                }

                .link-btn-wrapper:hover {
                    .el-link__inner {
                        color: #fff !important;
                    }
                }

                .json-wrapper {
                    margin-top: 8px;
                    overflow: auto;
                    border: 1px solid #ebeef5;
                    padding: 4px;
                    position: relative;

                    .social-module__viewer-ctrl {
                        position: absolute;
                        top: 8px;
                        right: 12px;
                    }
                }
            }
        }

        .kong-wrapper {
            padding-top: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            span {
                color: #aab4bf;
                font-size: 12px;
                line-height: 20px;
            }
        }
    }
</style>