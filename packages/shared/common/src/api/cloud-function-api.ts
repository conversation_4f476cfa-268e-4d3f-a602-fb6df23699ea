import BaseAPI from './base-api';

/**
* 云函数接口
*/
export class CloudFunctionAPI extends BaseAPI {
    /**
    * 根据函数名查询云函数定义
    * @param {string} name - name    
    */
    static getCloudFunctionViewByNameUsingGET(
        name:string,
    ) {
        return this.get<AbcAPI.CloudFunctionView>('/api/management/cloud-function/by-name', {
            params: {
                name,
            },
        });
    }
    
    /**
    * 执行云函数
    * @param {AbcAPI.CloudFunctionExecuteReq} reqBody - reqBody    
    */
    static executeUsingPOST(
        reqBody:AbcAPI.CloudFunctionExecuteReq,
    ) {
        return this.post<AbcAPI.CloudFunctionExecuteRsp>(
            '/api/management/cloud-function/execute',
            reqBody,
        );
    }
}