import { AddressChinaFunction } from '@/vendor/cloud-function';
import { convertAreaList, convertAreaTree } from '@/utils/area';
import { Toast } from 'vant';
import { onMounted, ref } from 'vue';

export default function useArea(isShortName = false) {
    const areaList = ref({});
    const areaTree = ref<any[]>([]);
    onMounted(async () => {
        const response = await AddressChinaFunction.exec();
        if (!response.status) {
            return Toast({
                type: 'fail',
                message: '获取地址列表失败',
            });
        }
        areaTree.value = convertAreaTree(response.data, isShortName);
        areaList.value = convertAreaList(response.data, isShortName);
    });

    return {
        areaList,
        areaTree,
    };
}
