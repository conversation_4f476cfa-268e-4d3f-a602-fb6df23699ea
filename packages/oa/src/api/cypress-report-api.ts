import BaseAPI from './base-api';

/**
 *
 */
export class CypressReportApi extends BaseAPI {
    /**
     * @desc 获取所有cypress测试报告
     * @param {string} offset -
     * @param {string} limit -
     */
    static getApiLowCodeCypressReports(
        offset?:string,
        limit?:string,
    ) {
        return this.get<AbcAPI.CypressReportListVo>('/api/low-code/cypress/report', {
            params: {
                offset,
                limit,
            },
        });
    }

    /**
     * @desc 获取报告详情
     * @param {string} id -
     */
    static getApiLowCodeCypressReportById(
        id:string,
    ) {
        return this.get<AbcAPI.CypressReport>(`/api/low-code/cypress/report/${id}`);
    }
}
