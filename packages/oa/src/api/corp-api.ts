import BaseAPI from './base-api';

/**
* 企业信息前端接口
*/
export class CorpAPI extends BaseAPI {
    /**
    * 根据群聊id查询群详情
    * @param {string} chatId - chatId
    */
    static getCustomerGroupDetailUsingGET(
        chatId:string,
    ) {
        return this.get<AbcAPI.GroupChat>(`/api/management/corp/customer-group-detail/${chatId}`);
    }

    /**
    * 企业微信商品类型接口
    */
    static getGoodsSkuCategoryListUsingGET(
    ) {
        return this.get<AbcAPI.AbcListPageMallGoodsCategoryView>('/api/management/corp/goods/category');
    }

    /**
    * 企业微信推荐类型接口
    */
    static getGoodsSkuRecommendListUsingGET1(
    ) {
        return this.get<AbcAPI.AbcListPageMallGoodsRecommendView>('/api/management/corp/goods/recommend');
    }

    /**
    * 企业微信商品推荐接口
    * @param {string} clinicId - 门店id
    * @param {string} categoryId - 商品类型
    * @param {string} keyword - 搜索关键词
    * @param {number} limit - limit
    * @param {number} offset - offset
    * @param {string} recommendId - 推荐类型
    */
    static getGoodsSkuRecommendListUsingGET(
        clinicId:string,
        categoryId?:string,
        keyword?:string,
        limit?:number,
        offset?:number,
        recommendId?:string,
    ) {
        return this.get<AbcAPI.AbcListPageMallGoodsSkuAbstractVO>(`/api/management/corp/goods/sku/${clinicId}`, {
            params: {
                categoryId,
                keyword,
                limit,
                offset,
                recommendId,
            },
        });
    }

    /**
    * 客服-查询客户基本信息
    * @param {AbcAPI.CustomerListReq} req - req
    */
    static getCustomerBatchByUserIdUsingGET(
        req:AbcAPI.CustomerListReq,
    ) {
        return this.get<AbcAPI.CustomerListRsp>(
            '/api/management/corp/kf/customer',
            { params: req },
        );
    }

    /**
    * 查询人员列表
    */
    static listCorpUserUsingGET(
    ) {
        return this.get<AbcAPI.AbcListPageCorpUserView>('/api/management/corp/list');
    }

    /**
    * 查询市场人员列表
    */
    static listCorpMarketerUsingGET(
    ) {
        return this.get<AbcAPI.AbcListPageCorpUserView>('/api/management/corp/list/marketer');
    }

    /**
    * 查询销售人员列表
    */
    static listCorpSellerUsingGET(
    ) {
        return this.get<AbcAPI.AbcListPageCorpUserView>('/api/management/corp/list/seller');
    }

    /**
    * 查询本人信息
    */
    static getCorpMeUsingGET(
    ) {
        return this.get<AbcAPI.CorpUserView>('/api/management/corp/me');
    }

    /**
    * 查询OSS-Token
    */
    static getOSSTokenUsingGET(
    ) {
        return this.get<AbcAPI.OSSToken>('/api/management/corp/oss-token');
    }

    /**
    * updateCorpContact
    */
    static updateCorpContactUsingGET(
    ) {
        return this.get<string>('/api/management/corp/update-contact');
    }

    /**
    * 更新jenkins token
    * @param {AbcAPI.CorpUpdateJenkinsTokenReq} reqBody - reqBody
    */
    static updateCorpJenkinsTokenUsingPUT(
        reqBody:AbcAPI.CorpUpdateJenkinsTokenReq,
    ) {
        return this.put<AbcAPI.CorpUpdateJenkinsTokenRsp>(
            '/api/management/corp/update-jenkins-token',
            reqBody,
        );
    }

    /**
    * 直接绑定外部用户和系统客户
    * @param {AbcAPI.CorpExternalEmployeeReq} req - req
    */
    static bindExternalUserAndEmployeeIdUsingPOST(
        req:AbcAPI.CorpExternalEmployeeReq,
    ) {
        return this.post<AbcAPI.CorpExternalUserEmployeeRelation>(
            '/api/management/corp/user/bind/employeeId',
            req,
        );
    }

    /**
    * 绑定external_userid和mobile手机号的关系
    * @param {AbcAPI.CorpExternalUserMobileReq} req - req
    */
    static bindExternalUserAndMobileUsingPUT(
        req:AbcAPI.CorpExternalUserMobileReq,
    ) {
        return this.put<AbcAPI.CorpExternalUserEmployeeRelation>(
            '/api/management/corp/user/bind/mobile/',
            req,
        );
    }

    /**
    * 根据external_userid获取门店的药品推广线索信息
    * @param {string} externalUserid - externalUserid
    */
    static getDrugPromotionCluesByExternalUserIdUsingGET(
        externalUserid:string,
    ) {
        return this.get<Array<AbcAPI.DrugPromotionCluesView>>(`/api/management/corp/user/drug-promotion-clues/${externalUserid}`);
    }

    /**
    * 根据external_userid获取客户信息及其全部门店信息
    * @param {string} externalUserid - externalUserid
    * @param {number} type - 0:1v1 客服,1:客户外部群群聊
    * @param {string} chatId - 群聊id
    */
    static getUserByExternalUseridUsingGET(
        externalUserid:string,
        type:number,
        chatId?:string,
    ) {
        return this.get<AbcAPI.EmployeeWithAllClinicsView>(`/api/management/corp/user/get-all-by-external-userid/${externalUserid}`, {
            params: {
                chatId,
                type,
            },
        });
    }

    /**
    * 根据mobile获取客户信息及其全部门店信息
    * @param {string} mobile - mobile
    */
    static getUserByMobileUsingGET(
        mobile:string,
    ) {
        return this.get<AbcAPI.EmployeeWithAllClinicsView>(`/api/management/corp/user/get-all-by-mobile/${mobile}`);
    }

    /**
    * 根据external_userid获取客户信息及最近登录门店信息
    * @param {string} externalUserid - externalUserid
    */
    static getUserByExternalUserIdUsingGET(
        externalUserid:string,
    ) {
        return this.get<AbcAPI.EmployeeA>(`/api/management/corp/user/get-by-external-userid/${externalUserid}`);
    }

    /**
    * 查询上一次群聊技术支持用户和顺序
    * @param {string} chatId - chatId
    */
    static getLastChatTicketUserUsingGET(
        chatId:string,
    ) {
        return this.get<AbcAPI.LastChatTicketUserView>(`/api/management/corp/user/get-last-chat-ticket/${chatId}`);
    }

    /**
    * 记录上一次群聊技术支持用户和顺序
    * @param {AbcAPI.LastChatTicketUserReq} req - req
    */
    static recordLastChatTicketUsingPOST(
        req:AbcAPI.LastChatTicketUserReq,
    ) {
        return this.post<AbcAPI.LastChatTicketUserView>(
            '/api/management/corp/user/record-last-chat-ticket',
            req,
        );
    }

    /**
    * 获取下属及其自己
    */
    static getUserStaffUsingGET(
    ) {
        return this.get<AbcAPI.AbcListPageCorpUserView>('/api/management/corp/user/staff');
    }

    /**
    * 直接解绑外部用户和系统客户
    * @param {AbcAPI.CorpExternalEmployeeReq} req - req
    */
    static unbindExternalUserAndEmployeeIdUsingPUT(
        req:AbcAPI.CorpExternalEmployeeReq,
    ) {
        return this.put<AbcAPI.CorpExternalUserEmployeeRelation>(
            '/api/management/corp/user/unbind/employeeId',
            req,
        );
    }
}
