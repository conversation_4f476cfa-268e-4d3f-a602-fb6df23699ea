import BaseAPI from './base-api';

/**
 * 发票接口
 */
export class InvoiceAPI extends BaseAPI {
    /**
     * disableSubmitInvoiceApply
     * @param {AbcAPI.DisableSubmitInvoiceApplyReq} req - req
     */
    static disableSubmitInvoiceApplyUsingPOST(
        req: AbcAPI.DisableSubmitInvoiceApplyReq,
    ) {
        return this.post<string>(
            '/api/management/invoice/apply/disable-submit',
            req,
        );
    }

    /**
     * exportClinicInvoiceApply
     * @param {AbcAPI.QueryClinicInvoiceApplyForQwReq} req - req
     */
    static exportClinicInvoiceApplyUsingPOST(
        req: AbcAPI.QueryClinicInvoiceApplyForQwReq,
    ) {
        return this.post<any>(
            '/api/management/invoice/apply/export',
            req,
        );
    }

    /**
     * pageQueryClinicInvoiceApply
     * @param {AbcAPI.QueryClinicInvoiceApplyForQwReq} req - req
     */
    static pageQueryClinicInvoiceApplyUsingPOST(
        req: AbcAPI.QueryClinicInvoiceApplyForQwReq,
    ) {
        return this.post<AbcAPI.AbcListPageClinicInvoiceApplyViewForQw>(
            '/api/management/invoice/apply/query',
            req,
        );
    }
}