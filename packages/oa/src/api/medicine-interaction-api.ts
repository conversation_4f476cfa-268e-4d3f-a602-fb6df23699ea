import BaseAPI from './base-api';

/**
*
*/
export class MedicineInteractionApi extends BaseAPI {
    /**
    * 获取相互作用
    * @param {string} ingredientId -
    * @param {string} offset -
    * @param {string} limit -
    * @param {string} ingredientTwoId -
    * @param {number} level -
    * @param {string} source -
    * @param {string} field -
    * @param {string} order -
    * @param {number} isClean -
    */
    static getApiWarehouseMedicineInteraction(
        ingredientId:string,
        offset:string,
        limit:string,
        ingredientTwoId?:string,
        level?:number,
        measures?:string,
        field?:string,
        order?:string,
        isClean?:number,
    ) {
        return this.get<AbcAPI.MedicineIngredientInteraction>('/api/warehouse/medicine/interaction', {
            params: {
                ingredientId,
                ingredientTwoId,
                level,
                measures,
                field,
                order,
                isClean,
                offset,
                limit,
            },
        });
    }

    /**
         * 创建相互作用
         * @param {AbcAPI.CreateInteractionDto[]} array -
         */
    static postApiWarehouseMedicineInteraction(
        array:AbcAPI.CreateInteractionDto[],
    ) {
        return this.post<AbcAPI.CreateInteractionDto>('/api/warehouse/medicine/interaction', array);
    }

    /**
         * 修改相互作用
         * @param {AbcAPI.CreateInteractionDto[]} array -
         */
    static putApiWarehouseMedicineInteraction(
        array:AbcAPI.CreateInteractionDto[],
    ) {
        return this.put<AbcAPI.CreateInteractionDto>('/api/warehouse/medicine/interaction', array);
    }

    /**
    * 删除相互作用
    * @param {string} interactionId -
    */
    static deleteApiWarehouseMedicineInteraction(
        interactionId:string,
    ) {
        return this.del<AbcAPI.CreateInteractionDto>('/api/warehouse/medicine/interaction', {
            params: {
                interactionId,
            },
        });
    }
}