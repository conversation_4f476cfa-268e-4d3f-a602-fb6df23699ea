import BaseAPI from './base-api';

/**
* Jenkins代理接口
*/
export class JenkinsAPI extends BaseAPI {
    /**
    * 代理接口
    */
    static proxyUsingGET(
    ) {
        return this.get<string>('/api/management/jenkins/proxy/**');
    }
    
    /**
    * 代理接口
    */
    static proxyUsingHEAD(
    ) {
        return this.head<string>('/api/management/jenkins/proxy/**');
    }
    
    /**
    * 代理接口
    */
    static proxyUsingPOST(
    ) {
        return this.post<string>('/api/management/jenkins/proxy/**');
    }
    
    /**
    * 代理接口
    */
    static proxyUsingPUT(
    ) {
        return this.put<string>('/api/management/jenkins/proxy/**');
    }
    
    /**
    * 代理接口
    */
    static proxyUsingDELETE(
    ) {
        return this.del<string>('/api/management/jenkins/proxy/**');
    }
    
    /**
    * 代理接口
    */
    static proxyUsingOPTIONS(
    ) {
        return this.options<string>('/api/management/jenkins/proxy/**');
    }
    
    /**
    * 代理接口
    */
    static proxyUsingPATCH(
    ) {
        return this.patch<string>('/api/management/jenkins/proxy/**');
    }
}