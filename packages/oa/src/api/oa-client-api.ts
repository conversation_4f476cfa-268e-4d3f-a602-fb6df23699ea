/**
* OaClientAPI
*/
export class OaClientAPI {
    /**
    * 查询客户人员排班客户通道列表
    * @param {string} servicerId - 客服人员id
    */
    static async sendMsg(msgType, payload, active = true) {
        const response = await fetch('http://127.0.0.1:8234/oa-client-api/sendMsg', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                __active__: active,
                __target__: 'app-tab-window',
                type: msgType,
                payload,
            }),
        });
        return response.json();
    }

    /**
     * 打电话
     * @param payload
     */
    static async callout(payload) {
        return this.sendMsg('callout', payload);
    }

    static async followUp(payload) {
        return this.sendMsg('followUp', payload);
    }

    static async remotely(payload) {
        return this.sendMsg('remotely', payload);
    }
}
