import BaseAPI from './base-api';

/**
 * 
 */
export class MedicineManualApi extends BaseAPI {
    /**
         * 获取说明书
         * @param {string} medicineId -          
         * @param {string} categoryId -          
         * @param {number} ruleType -          
         */
    static getApiWarehouseMedicineManual(
        medicineId?:string,
        categoryId?:string,
        ruleType?:number,
    ) {
        return this.get<AbcAPI.Manual>('/api/warehouse/medicine/manual', {
            params: {
                medicineId,
                categoryId,
                ruleType,
            },
        });
    }

    /**
    * 新增说明书
    * @param {AbcAPI.CreateManualDto} createManualDto -
    */
    static postApiWarehouseMedicineManual(
        createManualDto:AbcAPI.CreateManualDto,
    ) {
        return this.post<AbcAPI.Manual>('/api/warehouse/medicine/manual', createManualDto);
    }

    /**
    * 更新说明书
    * @param {AbcAPI.CreateManualDto} createManualDto -
    */
    static putApiWarehouseMedicineManual(
        createManualDto:AbcAPI.CreateManualDto,
    ) {
        return this.put<AbcAPI.Manual>('/api/warehouse/medicine/manual', createManualDto);
    }

    /**
    * 获取说明书tag
    * @param {string} parentTagId -
    * @param {string} tagName -
    */
    static getApiWarehouseMedicineManualTag(
        parentTagId:string,
        tagName:string,
    ) {
        return this.get<AbcAPI.ManualTag>('/api/warehouse/medicine/manual/tag', {
            params: {
                parentTagId,
                tagName,
            },
        });
    }

    /**
         * 创建说明书tag
         * @param {AbcAPI.ManualTagListDto} manualTagListDto -
         */
    static postApiWarehouseMedicineManualTag(
        manualTagListDto:AbcAPI.ManualTagListDto,
    ) {
        return this.post<AbcAPI.ManualTag>('/api/warehouse/medicine/manual/tag', manualTagListDto);
    }
        
    /**
         * 修改说明书tag
         * @param {AbcAPI.ManualTagListDto} manualTagListDto -          
         */
    static putApiWarehouseMedicineManualTag(
        manualTagListDto:AbcAPI.ManualTagListDto,
    ) {
        return this.put<AbcAPI.ManualTag>('/api/warehouse/medicine/manual/tag', manualTagListDto);
    }
        
    /**
         * 删除说明书tag
         * @param {string} id -          
         */
    static deleteApiWarehouseMedicineManualTag(
        id:string,
    ) {
        return this.del<AbcAPI.ManualTag>('/api/warehouse/medicine/manual/tag', {
            params: {
                id,
            },
        });
    }
        
    /**
         * 获取所有说明书tag
         */
    static getApiWarehouseMedicineManualTagList(
    ) {
        return this.get<AbcAPI.ManualTag>('/api/warehouse/medicine/manual/tag/list');
    }
        
    /**
    * 获取基础说明书tag
    */
    static getApiWarehouseMedicineManualBaseTag(
    ) {
        return this.get<AbcAPI.ManualTag>('/api/warehouse/medicine/manual/baseTag');
    }
        
    /**
    * 获取说明书简介tag
    */
    static getApiWarehouseMedicineManualBasicTag(
    ) {
        return this.get<AbcAPI.ManualTag>('/api/warehouse/medicine/manual/basicTag');
    }
        
    /**
         * 获取说明书内容
         * @param {string} tagId -          
         * @param {string} manualId -          
         */
    static getApiWarehouseMedicineManualContent(
        tagId:string,
        manualId:string,
    ) {
        return this.get<AbcAPI.ManualContent>('/api/warehouse/medicine/manual/content', {
            params: {
                tagId,
                manualId,
            },
        });
    }
}