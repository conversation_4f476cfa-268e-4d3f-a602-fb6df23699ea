import BaseAPI from './base-api';

/**
* 停机公告
*/
export class MaintainNoticeAPI extends BaseAPI {
    /**
    * 列表
    * @param {number} limit - 分页条数    
    * @param {number} offset - 偏移量    
    * @param {number} type - 1故障公告；2维护公告；3医保公告    
    */
    static pageListMaintainNoticeUsingGET(
        limit:number,
        offset:number,
        type:number,
    ) {
        return this.get<AbcAPI.AbcListPageMaintainNoticeView>('/api/management/maintain-notice', {
            params: {
                limit,
                offset,
                type,
            },
        });
    }
    
    /**
    * 新增
    * @param {AbcAPI.MaintainNoticeUpsertReq} req - req    
    */
    static addMaintainNoticeUsingPOST(
        req:AbcAPI.MaintainNoticeUpsertReq,
    ) {
        return this.post<AbcAPI.MaintainNoticeView>('/api/management/maintain-notice', req);
    }
    
    /**
    * 详情
    * @param {string} id - id    
    */
    static getMaintainNoticeUsingGET(
        id:string,
    ) {
        return this.get<AbcAPI.MaintainNoticeView>(`/api/management/maintain-notice/${id}`);
    }
    
    /**
    * 修改
    * @param {string} id - id    
    * @param {AbcAPI.MaintainNoticeUpsertReq} req - req    
    */
    static updateMaintainNoticeUsingPUT(
        id:string,
        req:AbcAPI.MaintainNoticeUpsertReq,
    ) {
        return this.put<AbcAPI.MaintainNoticeView>(`/api/management/maintain-notice/${id}`, req);
    }
    
    /**
    * 删除
    * @param {string} id - id    
    */
    static deleteMaintainNoticeUsingDELETE(
        id:string,
    ) {
        return this.del<AbcAPI.BaseOptionRsp>(`/api/management/maintain-notice/${id}`);
    }
    
    /**
    * 发布预告
    * @param {string} id - id    
    */
    static prePublishMaintainNoticeUsingPUT(
        id:string,
    ) {
        return this.put<AbcAPI.BaseOptionRsp>(`/api/management/maintain-notice/${id}/pre-publish`);
    }
    
    /**
    * 发布公告
    * @param {string} id - id    
    */
    static publishMaintainNoticeUsingPUT(
        id:string,
    ) {
        return this.put<AbcAPI.BaseOptionRsp>(`/api/management/maintain-notice/${id}/publish`);
    }
    
    /**
    * 状态变更
    * @param {string} id - id    
    * @param {number} status - 1停机中; 2恢复中； 3撤回 4正常；    
    */
    static changeMaintainNoticeStatusUsingPUT(
        id:string,
        status:number,
    ) {
        return this.put<AbcAPI.MaintainNoticeView>(`/api/management/maintain-notice/${id}/status?status=${status}`);
    }
}