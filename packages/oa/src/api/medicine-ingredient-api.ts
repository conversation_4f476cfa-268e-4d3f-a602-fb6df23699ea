import BaseAPI from './base-api';

/**
* 
*/
export class MedicineIngredientApi extends BaseAPI {
    /**
    * 获取所有成分
    * @param {string} ingredient - 成分名称    
    * @param {string} ingredientId -     
    * @param {string} ingredientPrefix -     
    */
    static getApiWarehouseMedicineIngredient(
        ingredient?:string,
        ingredientId?:string,
        ingredientPrefix?:string,
    ) {
        return this.get<AbcAPI.ManualMedicineIngredient>('/api/warehouse/medicine/ingredient', {
            params: {
                ingredient,
                ingredientId,
                ingredientPrefix,
            },
        });
    }
    
    /**
    * 创建成分
    * @param {string} string -     
    */
    static postApiWarehouseMedicineIngredient(
        string:string,
    ) {
        return this.post<AbcAPI.ManualMedicineIngredient>('/api/warehouse/medicine/ingredient', string);
    }
    
    /**
    * 更新成分
    */
    static putApiWarehouseMedicineIngredient(
    ) {
        return this.put<AbcAPI.ManualMedicineIngredient>('/api/warehouse/medicine/ingredient');
    }
    
    /**
    * 删除成分
    * @param {string} ingredientId -     
    */
    static deleteApiWarehouseMedicineIngredient(
        ingredientId:string,
    ) {
        return this.del<number>('/api/warehouse/medicine/ingredient', {
            params: {
                ingredientId,
            },
        });
    }
}