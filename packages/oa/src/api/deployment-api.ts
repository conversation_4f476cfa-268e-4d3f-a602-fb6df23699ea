import BaseAPI from './base-api';

/**
*
*/
export class Deployment<PERSON><PERSON> extends BaseAPI {
    /**
    * 创建tag
    * @param {AbcAPI.CreateDeployTagDto} createDeployTagDto -
    */
    static postApiLowCodeDeploymentDeployTag(
        createDeployTagDto:AbcAPI.CreateDeployTagDto,
    ) {
        return this.post<AbcAPI.DeployTag>(
            '/api/low-code/deployment/deploy-tag',
            createDeployTagDto,
        );
    }

    /**
    * 获取 tag 列表
    */
    static getApiLowCodeDeploymentDeployTag(
    ) {
        return this.get<AbcAPI.DeployTagListVo>('/api/low-code/deployment/deploy-tag');
    }

    /**
    * 更新tag信息
    * @param {AbcAPI.UpdateDeployTagDto} updateDeployTagDto -
    * @param {string} id -
    */
    static putApiLowCodeDeploymentDeployTagById(
        updateDeployTagDto:AbcAPI.UpdateDeployTagDto,
        id:string,
    ) {
        return this.put<AbcAPI.DeployTag>(
        `/api/low-code/deployment/deploy-tag/${id}`,
        updateDeployTagDto,
        );
    }

    /**
    * 删除tag
    * @param {string} id -
    */
    static deleteApiLowCodeDeploymentDeployTagById(
        id:string,
    ) {
        return this.del<AbcAPI.DeployTag>(`/api/low-code/deployment/deploy-tag/${id}`);
    }

    /**
    * 更新tag状态
    * @param {AbcAPI.UpdateDeployTagStatusByIdDto} updateDeployTagStatusByIdDto -
    * @param {string} id -
    */
    static putApiLowCodeDeploymentDeployTagByIdStatus(
        updateDeployTagStatusByIdDto:AbcAPI.UpdateDeployTagStatusByIdDto,
        id:string,
    ) {
        return this.put<AbcAPI.DeployTag>(
        `/api/low-code/deployment/deploy-tag/${id}/status`,
        updateDeployTagStatusByIdDto,
        );
    }

    /**
    * 审批tag
    * @param {AbcAPI.UpdateDeployTagApprovalStatusDto} updateDeployTagApprovalStatusDto -
    * @param {string} id -
    */
    static putApiLowCodeDeploymentDeployTagByIdApproved(
        updateDeployTagApprovalStatusDto:AbcAPI.UpdateDeployTagApprovalStatusDto,
        id:string,
    ) {
        return this.put<AbcAPI.DeployTag>(
        `/api/low-code/deployment/deploy-tag/${id}/approved`,
        updateDeployTagApprovalStatusDto,
        );
    }

    /**
    * 获取发布环境列表
    */
    static getApiLowCodeDeploymentDeployTagEnvironment(
    ) {
        return this.get<AbcAPI.DeployTagEnvironmentListVo>('/api/low-code/deployment/deploy-tag/environment');
    }

    /**
    * 添加发布环境列表
    * @param {AbcAPI.CreateDeployTagEnvironmentDto} createDeployTagEnvironmentDto -
    */
    static postApiLowCodeDeploymentDeployTagEnvironment(
        createDeployTagEnvironmentDto:AbcAPI.CreateDeployTagEnvironmentDto,
    ) {
        return this.post<AbcAPI.DeployTagEnvironment>(
            '/api/low-code/deployment/deploy-tag/environment',
            createDeployTagEnvironmentDto,
        );
    }

    /**
    * 创建发布任务
    * @param {AbcAPI.CreateDeployTaskDto} createDeployTaskDto -
    */
    static postApiLowCodeDeploymentDeployTask(
        createDeployTaskDto:AbcAPI.CreateDeployTaskDto,
    ) {
        return this.post<AbcAPI.DeployTask>(
            '/api/low-code/deployment/deploy-task',
            createDeployTaskDto,
        );
    }

    /**
    * 获取所有发布任务
    * @param {number} type -
    * @param {string} offset -
    * @param {string} limit -
    */
    static getApiLowCodeDeploymentDeployTask(
        type:number,
        offset?:string,
        limit?:string,
    ) {
        return this.get<AbcAPI.DeployTaskListVo>('/api/low-code/deployment/deploy-task', {
            params: {
                type,
                offset,
                limit,
            },
        });
    }

    /**
    * 获取发布任务详情
    * @param {string} id -
    */
    static getApiLowCodeDeploymentDeployTaskById(
        id:string,
    ) {
        return this.get<AbcAPI.DeployTaskVo>(`/api/low-code/deployment/deploy-task/${id}`);
    }

    /**
    * 更新发布任务
    * @param {AbcAPI.UpdateDeployTaskDto} updateDeployTaskDto -
    * @param {string} id -
    */
    static patchApiLowCodeDeploymentDeployTaskById(
        updateDeployTaskDto:AbcAPI.UpdateDeployTaskDto,
        id:string,
    ) {
        return this.patch<AbcAPI.DeployTask>(
        `/api/low-code/deployment/deploy-task/${id}`,
        updateDeployTaskDto,
        );
    }

    /**
    * 获取当前打开的发布任务
    */
    static getApiLowCodeDeploymentDeployTaskOpened(
    ) {
        return this.get<AbcAPI.DeployTaskVo>('/api/low-code/deployment/deploy-task/opened');
    }

    /**
    * 更新发布任务状态
    * @param {AbcAPI.UpdateDeployTaskStatusDto} updateDeployTaskStatusDto -
    * @param {string} id -
    */
    static patchApiLowCodeDeploymentDeployTaskByIdUpdateStatus(
        updateDeployTaskStatusDto:AbcAPI.UpdateDeployTaskStatusDto,
        id:string,
    ) {
        return this.patch<AbcAPI.DeployTask>(
        `/api/low-code/deployment/deploy-task/${id}/update-status`,
        updateDeployTaskStatusDto,
        );
    }

    /**
    * 获取班车对应的需求分组
    * @param {string} id -
    */
    static getApiLowCodeDeploymentDeployTaskByIdGroupOptions(
        id:string,
    ) {
        return this.get<AbcAPI.DeployTaskGroupOptionsVo>(`/api/low-code/deployment/deploy-task/${id}/group-options`);
    }

    /**
    * 设置班车对应的需求分组
    * @param {AbcAPI.CreateDeployTaskDto} createDeployTaskDto -
    * @param {string} id -
    */
    static postApiLowCodeDeploymentDeployTaskByIdGroupOptions(
        createDeployTaskDto:AbcAPI.CreateDeployTaskDto,
        id:string,
    ) {
        return this.post<AbcAPI.DeployTaskGroupOptionsVo>(
        `/api/low-code/deployment/deploy-task/${id}/group-options`,
        createDeployTaskDto,
        );
    }

    /**
    * 批量创建JenkinsInfo
    * @param {AbcAPI.BatchCreateDeployJenkinsInfoDto} batchCreateDeployJenkinsInfoDto -
    */
    static postApiLowCodeDeploymentDeployJenkinsInfoBatchCreate(
        batchCreateDeployJenkinsInfoDto:AbcAPI.BatchCreateDeployJenkinsInfoDto,
    ) {
        return this.post<boolean>(
            '/api/low-code/deployment/deploy-jenkins-info/batch-create',
            batchCreateDeployJenkinsInfoDto,
        );
    }

    /**
    * 批量查询JenkinsInfo
    * @param {string} env -
    */
    static getApiLowCodeDeploymentDeployJenkinsInfo(
        env:string,
    ) {
        return this.get<AbcAPI.DeployTagListVo>('/api/low-code/deployment/deploy-jenkins-info', {
            params: {
                env,
            },
        });
    }

    static getApiLowCodeDeploymentRecord(
        params: { offset: number; limit: number; date?:string,
            status?:number | string,
            keyword?:string,
        },
    ) {
        return this.get<AbcAPI.DeployTaskListVo>('/api/low-code/deployment/deploy-platform/record', {
            params,
        });
    }
}
