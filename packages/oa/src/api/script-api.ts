import BaseAPI from './base-api';

/**
* 
*/
export class <PERSON>riptApi extends BaseAPI {
    /**
    * 创建脚本
    * @param {AbcAPI.CreateScriptDto} createScriptDto -     
    */
    static postApiLowCodeScript(
        createScriptDto:AbcAPI.CreateScriptDto,
    ) {
        return this.post<AbcAPI.Script>(
            '/api/low-code/script',
            createScriptDto,
        );
    }
    
    /**
    * 
    */
    static getApiLowCodeScript(
    ) {
        return this.get<AbcAPI.ScriptListVo>('/api/low-code/script');
    }
    
    /**
    * 
    * @param {string} id -     
    */
    static getApiLowCodeScriptById(
        id:string,
    ) {
        return this.get<AbcAPI.Script>(`/api/low-code/script/${id}`);
    }
    
    /**
    * 
    * @param {AbcAPI.UpdateScriptDto} updateScriptDto -     
    * @param {string} id -     
    */
    static patchApiLowCodeScriptById(
        updateScriptDto:AbcAPI.UpdateScriptDto,
        id:string,
    ) {
        return this.patch<AbcAPI.Script>(
        `/api/low-code/script/${id}`,
        updateScriptDto,
        );
    }
    
    /**
    * 
    * @param {string} id -     
    */
    static deleteApiLowCodeScriptById(
        id:string,
    ) {
        return this.del<number>(`/api/low-code/script/${id}`);
    }
    
    /**
    * 根据脚本名执行脚本
    * @param {AbcAPI.ExecScriptDto} execScriptDto -     
    */
    static postApiLowCodeScriptExec(
        execScriptDto:AbcAPI.ExecScriptDto,
    ) {
        return this.post<AbcAPI.ExecScriptVo>(
            '/api/low-code/script/exec',
            execScriptDto,
        );
    }
    
    /**
    * 预览脚本执行结果
    * @param {AbcAPI.ExecPreviewScriptDto} execPreviewScriptDto -     
    */
    static postApiLowCodeScriptExecPreview(
        execPreviewScriptDto:AbcAPI.ExecPreviewScriptDto,
    ) {
        return this.post<AbcAPI.ExecScriptVo>(
            '/api/low-code/script/exec-preview',
            execPreviewScriptDto,
        );
    }
}