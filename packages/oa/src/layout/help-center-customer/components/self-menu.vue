<script setup lang="ts">
import { useHelpCenter } from '@/store/help-center-customer';
import { storeToRefs } from 'pinia';

const helpCenter = useHelpCenter();

const curCetegoryId = storeToRefs(helpCenter).curCetegoryId;

type MenuItem = {
    category: string;
    id: string;
    children?: Array<MenuItem>
    isOpened?: boolean;
}

defineProps<{
    menuItem: MenuItem;
}>();

</script>
<template>
    <div>
        <template v-if="!menuItem?.children?.length">
            <el-menu-item
                :index="menuItem.id"
                class="help-center-customer-aside__menu-item"
                :class="{
                    active: menuItem.id === curCetegoryId
                }"
            >
                <span class="menu-item-icon-wrapper"></span>
                <span class="menu-item-text"> {{ menuItem.category }}</span>
            </el-menu-item>
        </template>
        <template v-else>
            <el-sub-menu
                :index="menuItem.id"
                class="help-center-customer-aside__menu-item"
                :class="{
                    active: menuItem.id === curCetegoryId
                }"
                :data-test="menuItem.id === curCetegoryId"
            >
                <template #title>
                    <span class="menu-item-icon-wrapper">
                        <i :class="menuItem.isOpened ? 'ri-arrow-down-s-fill': 'ri-arrow-right-s-fill'"></i>
                    </span>
                    <span class="menu-item-text"> {{ menuItem.category }}</span>
                </template>
                <self-menu
                    v-for="m in menuItem.children"
                    :key="m.id"
                    :menu-item="m"
                ></self-menu>
            </el-sub-menu>
        </template>
    </div>
</template>
<style lang="scss">
    .help-center-customer-aside__menu-item {
        border-radius: 6px;

        .el-sub-menu__title {
            height: 40px;
            line-height: 40px;
        }

        .menu-item-icon-wrapper {
            width: 12px;
            height: 12px;
            display: inline-block;
            margin-right: 8px;
            position: relative;

            & > i {
                font-size: 12px;
                line-height: 12px;
                color: #aab4bf;
                position: absolute;
                top: 0;
            }
        }

        .el-icon.el-sub-menu__icon-arrow {
            display: none;
        }

        &.is-opened {
            height: auto;
        }

        &.active {
            background: #e9f2fe;

            & > .menu-item-text {
                color: #0090ff;
            }

            .el-sub-menu__title {
                & > .menu-item-text {
                    color: #0090ff;
                    font-weight: bold;
                }
            }
        }

        &.el-menu-item {
            height: 40px;
            line-height: 40px;

            &.active {
                .menu-item-text {
                    color: #0090ff;
                    font-weight: bold;
                }
            }
        }

        &.el-sub-menu {
            .el-sub-menu__title {
                &:hover {
                    background: none;
                }
            }
        }
    }
</style>