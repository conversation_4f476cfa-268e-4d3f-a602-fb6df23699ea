<script setup lang="ts">
import { useFormat } from '@/composables/date';
import { useField } from '@/vendor/x-form/core/field';
import dayjs from 'dayjs';
import { computed, ref } from 'vue';
import H5Field from './h5-field.vue';

const props = defineProps({
    modelValue: {
        type: String,
    },
    minDate: {
        type: Date,
        default() {
            return dayjs().subtract(1, 'year').toDate();
        },
    },
    maxDate: {
        type: Date,
        default() {
            return new Date();
        },
    },
    showConfirm: {
        type: Boolean,
        default: false,
    },
});

const field = useField();

const emit = defineEmits(['update:modelValue']);

const defaultDate = computed(() => dayjs(props.modelValue).toDate());

const visibleCalendar = ref(false);

function showCalendar() {
    if (field.disable || field.readonly) {
        return;
    }
    visibleCalendar.value = true;
}

function onConfirm(date: Date) {
    visibleCalendar.value = false;
    emit('update:modelValue', useFormat(date));
}

</script>
<template>
    <h5-field @click="showCalendar"></h5-field>
    <van-calendar
        v-model:show="visibleCalendar"
        :title="field.label"
        color="var(--oa-primary-color)"
        :default-date="defaultDate"
        :min-date="minDate"
        :max-date="maxDate"
        :show-confirm="showConfirm"
        :lazy-render="false"
        allow-same-day
        @confirm="onConfirm"
    ></van-calendar>
</template>
