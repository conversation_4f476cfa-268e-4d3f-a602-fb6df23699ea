<script setup lang="ts">
import EmployeeCard from '@/components/employee-card.vue';
import { Toast } from 'vant';
import { computed, reactive } from 'vue';
import { CrmClientApi } from '@/api/crm-client-api';
import H5Select from './select.vue';

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
});

const vmData = reactive({
    selectOptions: [],
});

const emit = defineEmits(['update:modelValue']);

const currentValue = computed<string>({
    set(val) {
        emit('update:modelValue', val);
    },
    get() {
        return props.modelValue;
    },
});

async function fetchSelectOptions(keyword: string) {
    let res: any = {};
    try {
        res = await CrmClientApi.getApiLowCodeCrmEmployeeList(keyword);
    } catch (err: any) {
        Toast.fail(err);
        return;
    }
    vmData.selectOptions = res.rows?.map((row: any) => ({
        ...row,
        label: row.name,
        value: row.id,
    })) || [];
}

</script>
<template>
    <h5-select
        v-model="currentValue"
        filterable
        remote
        :remote-method="fetchSelectOptions"
        :options="vmData.selectOptions"
    >
        <template #option="{option}">
            <employee-card :employee="option"></employee-card>
        </template>
    </h5-select>
</template>

<style>

</style>
