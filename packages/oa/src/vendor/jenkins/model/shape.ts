export interface JenkinsOptions {
    baseUrl: string;
}

export enum JenkinsType {
    Hudson = 'Hudson',
    Folder = 'Folder',
    WorkflowJob = 'WorkflowJob',
    FreeStyleProject = 'FreeStyleProject',
    ParametersDefinitionProperty = 'ParametersDefinitionProperty',
    Unknown = ''
}

export interface JenkinsParameter {
    _class: string;
    type: string;
    name: string;
    defaultValue: string;
    description: string;
    choices?: Array<string>;
}

export interface JenkinsBuild {
    _class: string
    number: number
    url: string
    result: string
    timestamp: number
    duration: number
    estimatedDuration: number
    building: boolean,
    buildUserId: string;
    buildUserName: string;
}

export enum JenkinsParameterType {
    WReadonlyStringParameterDefinition = 'WReadonlyStringParameterDefinition',
    StringParameterDefinition = 'StringParameterDefinition',
    ChoiceParameterDefinition = 'ChoiceParameterDefinition',
}
