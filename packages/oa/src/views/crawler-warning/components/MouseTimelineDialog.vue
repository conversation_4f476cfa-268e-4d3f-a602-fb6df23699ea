<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElDialog, ElTimeline, ElTimelineItem, ElTag, ElEmpty, ElButton, ElButtonGroup } from 'element-plus';
import { formatDate } from '@abc-oa/utils';

const props = defineProps<{
  visible: boolean;
  trackData?: any;
  warningInfo?: any;
  rawEventData?: any; // 原始事件数据，包含url等信息
}>();

const emit = defineEmits(['update:visible']);

const dialogVisible = ref(false);
const filterType = ref<'all' | 'click' | 'move'>('all');

watch(() => props.visible, (val) => {
    dialogVisible.value = val;
});

watch(() => dialogVisible.value, (val) => {
    emit('update:visible', val);
});

// 计算时间轴数据
const timelineData = computed(() => {
    if (!props.trackData || !props.trackData.positions || props.trackData.positions.length === 0) {
        return [];
    }

    let filteredPositions = props.trackData.positions;
    
    // 根据过滤类型筛选数据
    if (filterType.value === 'click') {
        filteredPositions = props.trackData.positions.filter((pos: any) => pos.type === 'click');
    } else if (filterType.value === 'move') {
        filteredPositions = props.trackData.positions.filter((pos: any) => pos.type === 'move');
    }

    return filteredPositions.map((position: any, index: number) => {
        const timestamp = position.timestamp;
        const time = formatDate(new Date(+timestamp), 'YYYY-MM-DD HH:mm:ss');
        const operationType = position.type === 'click' ? '点击' : '移动';
        const coordinates = `(${position.x}, ${position.y})`;
        
        // 构建描述信息
        let description = `在页面坐标 ${coordinates} 处${operationType}鼠标`;
        
        // 如果是点击操作且有dc字段，添加点击内容信息
        if (position.type === 'click' && position.dc) {
            description += `，点击了"${position.dc}"`;
        }
        
        // 如果有URL信息，添加页面信息
        if (position.url) {
            description += `（页面：${position.url}）`;
        }
        
        return {
            time,
            timestamp,
            type: position.type,
            operationType,
            coordinates,
            description,
            url: position.url || '',
            clickContent: position.dc || '',
            index: index + 1,
        };
    });
});

// 获取操作类型标签颜色
const getTypeTagColor = (type: string) => (type === 'click' ? 'danger' : 'info');

</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        title="鼠标操作时间轴"
        width="700px"
        destroy-on-close
        append-to-body
    >
        <div v-if="timelineData.length > 0" class="timeline-container">
            <div class="timeline-header">
                <div class="timeline-stats">
                    <span>总操作数：{{ timelineData.length }}</span>
                    <span>点击次数：{{ timelineData.filter(item => item.type === 'click').length }}</span>
                    <span>移动次数：{{ timelineData.filter(item => item.type === 'move').length }}</span>
                </div>
                <div class="filter-buttons">
                    <el-button-group>
                        <el-button 
                            :type="filterType === 'all' ? 'primary' : 'default'"
                            size="small"
                            @click="filterType = 'all'"
                        >
                            全部
                        </el-button>
                        <el-button 
                            :type="filterType === 'click' ? 'danger' : 'default'"
                            size="small"
                            @click="filterType = 'click'"
                        >
                            点击
                        </el-button>
                        <el-button 
                            :type="filterType === 'move' ? 'info' : 'default'"
                            size="small"
                            @click="filterType = 'move'"
                        >
                            移动
                        </el-button>
                    </el-button-group>
                </div>
            </div>
            
            <el-timeline class="mouse-timeline">
                <el-timeline-item
                    v-for="item in timelineData"
                    :key="item.index"
                    :timestamp="item.time"
                    placement="top"
                    :type="item.type === 'click' ? 'danger' : 'primary'"
                    size="large"
                >
                    <div class="timeline-content">
                        <div class="operation-header">
                            <el-tag :type="getTypeTagColor(item.type)" size="small">
                                {{ item.operationType }}
                            </el-tag>
                            <span class="operation-index">#{{ item.index }}</span>
                        </div>
                        <div class="operation-description">
                            {{ item.description }}
                        </div>
                        <div class="operation-details">
                            <span v-if="item.url" class="detail-item">页面: {{ item.url }}</span>
                            <span v-if="item.clickContent" class="detail-item">点击内容: {{ item.clickContent }}</span>
                            <span class="detail-item">时间戳: {{ item.timestamp }}</span>
                        </div>
                    </div>
                </el-timeline-item>
            </el-timeline>
        </div>
        <el-empty v-else description="暂无鼠标操作数据" />
        
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
.timeline-container {
    max-height: 500px;
    overflow-y: auto;
}

.timeline-header {
    margin-bottom: 20px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .timeline-stats {
        display: flex;
        gap: 20px;
        font-size: 14px;
        color: #606266;

        span {
            font-weight: 500;
        }
    }

    .filter-buttons {
        display: flex;
        align-items: center;
    }
}

.mouse-timeline {
    padding: 0 12px;

    :deep(.el-timeline-item__timestamp) {
        font-weight: 500;
        color: #409eff;
    }

    :deep(.el-timeline-item__node) {
        font-size: 16px;
    }
}

.timeline-content {
    padding: 8px 0;

    .operation-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 6px;

        .operation-index {
            font-size: 12px;
            color: #909399;
            font-weight: 500;
        }
    }

    .operation-description {
        font-size: 14px;
        color: #303133;
        margin-bottom: 6px;
        line-height: 1.4;
    }

    .operation-details {
        display: flex;
        gap: 16px;
        font-size: 12px;
        color: #909399;

        .detail-item {
            background: #f5f7fa;
            padding: 2px 6px;
            border-radius: 3px;
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}
</style>
