<script lang="ts" setup>
import dayjs from 'dayjs';
import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { <PERSON> } from '@/vendor/jenkins';
import { JenkinsBuild } from '@/vendor/jenkins/model/shape';

const jenkins = new <PERSON>();

const props = defineProps<{
    build: JenkinsBuild,
}>();

const emit = defineEmits(['update:build']);

const passedTime = ref(0);

const percentProgress = computed(() => Math.min(
    Math.floor(passedTime.value * 100 / (props.build.estimatedDuration / 1000)),
    100,
));

// eslint-disable-next-line max-len
const progressPivotText = computed(() => `${Math.floor(passedTime.value)}/${Math.ceil(props.build.estimatedDuration / 1000)}s`);

const visibleBuildLog = ref(false);

const buildLogDom = ref();

const buildLog = reactive({
    data: '',
    moreData: false,
    textSize: 0,
});

async function showBuildLog() {
    visibleBuildLog.value = true;
    await fetchBuildLog();
}

let buildLogTimer: any;
async function fetchBuildLog() {
    const { data, moreData, textSize } = await jenkins.getBuildLog(props.build.url);
    buildLog.data = data;
    buildLog.moreData = moreData;
    // 内容是否发生改变
    let changed = buildLog.textSize !== textSize;
    buildLog.textSize = textSize;
    if (changed) {
        nextTick(() => {
            buildLogDom.value.scrollTop = 9999;
        });
    }

    if (moreData) {
        buildLogTimer = setTimeout(() => {
            fetchBuildLog();
        }, 1500);
    }
}

async function stopBuildLogTimer() {
    buildLogTimer && clearTimeout(buildLogTimer);
}

let startTimer = false;
onMounted(() => {
    watch(() => props.build, (newBuild) => {
        if (newBuild.building) {
            passedTime.value = (Date.now() - newBuild.timestamp) / 1000;
            // 查询构建进度
            if (!startTimer) {
                startTimer = true;
                startQueryProgress();
            }
        } else {
            // 停止查询进度
            stopQueryProgress();
        }
    }, {
        immediate: true,
        deep: true,
    });
});

onUnmounted(() => {
    stopQueryProgress();
    stopBuildLogTimer();
});

let progressTimer: any;

/**
 * 开始查询进度
 */
async function startQueryProgress() {
    stopQueryProgress();
    if (props.build.building) {
        await queryProgress();
        progressTimer = setTimeout(async () => {
            await startQueryProgress();
        }, 2000);
    }
}

/**
 * 查询进度
 */
async function queryProgress() {
    const buildInfo = await jenkins.getBuildInfo(props.build.url);
    emit('update:build', buildInfo);
}

/**
 * 停止查询进度
 */
function stopQueryProgress() {
    progressTimer && clearTimeout(progressTimer);
}

/**
 * 停止构建
 */
function handleStopBuildClick() {
    jenkins.stopBuild(props.build.url);
}
</script>

<template>
    <div
        :class="{
            'build-info-card-wrapper': true,
            'failed' : build.result === 'FAILURE',
            'success' : build.result === 'SUCCESS',
            'aborted' : build.result === 'ABORTED',
            'building' : build.building === true,
        }"
        @click="showBuildLog"
    >
        <van-row class="build-basic-info" align="center">
            <van-col class="build-number">#{{ build.number }}</van-col>
            <van-col class="build-user" :offset="1">{{ build.buildUserName }}</van-col>
            <van-col class="build-start-time">{{ dayjs(build.timestamp).format('YYYY-MM-DD HH:mm:ss') }}</van-col>
        </van-row>
        <van-row align="center">
            <div v-if="build.building" class="progress-wrapper">
                <van-row>
                    <van-col :span="22">
                        <van-progress
                            :percentage="percentProgress"
                            :pivot-text="progressPivotText"
                            color="var(--oa-orange)"
                            stroke-width="8"
                        >
                        </van-progress>
                    </van-col>
                    <van-icon
                        class="stop-btn"
                        name="stop-circle-o"
                        size="24"
                        color="var(--oa-warning-color)"
                        @click.stop="handleStopBuildClick"
                    />
                </van-row>
            </div>
            <template v-else>
                {{ build.duration / 1000 }}s
            </template>
        </van-row>
        <van-popup
            v-model:show="visibleBuildLog"
            position="bottom"
            closeable
            close-on-popstate
            safe-area-inset-bottom
            :style="{height: '90%'}"
            class="build-log-popup"
            teleport="#app"
            @close="stopBuildLogTimer"
        >
            <h2 class="build-log-title">#{{ build.number }}构建日志</h2>
            <pre ref="buildLogDom" class="build-log-wrapper">
            <div v-html="buildLog.data"></div>
        </pre>
            <div v-if="buildLog.moreData" class="build-log-loading-wrapper">
                <van-loading type="spinner" size="14">构建中...</van-loading>
            </div>
        </van-popup>
    </div>
</template>

<style lang="scss">
    .build-info-card-wrapper {
        margin-bottom: 8px;
        border-radius: var(--oa-border-radius-4);
        overflow: hidden;
        padding: var(--oa-padding-8);

        &.building {
            color: var(--oa-text-color);
            background-color: var(--oa-white);
        }

        &.success {
            color: white;
            background-color: var(--oa-success-color);
        }

        &.failed {
            color: white;
            background-color: var(--oa-danger-color);
        }

        &.aborted {
            color: white;
            background-color: var(--oa-gray-5);
        }

        .van-row {
            height: 24px;
        }

        .build-basic-info {
            display: flex;

            .build-number {
                font-size: var(--oa-font-size-18);
                font-weight: bold;
            }

            .build-start-time {
                margin-left: auto;
            }
        }

        .progress-wrapper {
            height: 100%;
            width: 100%;
            padding: 8px;

            .stop-btn {
                position: relative;
                left: 12px;
                bottom: 8px;

                &:active {
                    color: var(--oa-red-light) !important;
                }
            }
        }
    }

    .build-log-popup {
        display: flex;
        flex-direction: column;

        h2.build-log-title {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 48px;
            margin-bottom: 0;
        }

        .build-log-wrapper {
            padding: 0 var(--oa-padding-8);
            font-size: var(--oa-font-size-12);
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-y: auto;
            flex: 1;

            .pipeline-new-node {
                display: block;
                margin: var(--oa-padding-4) 0;
                color: var(--oa-text-color-2);
            }
        }

        .build-log-loading-wrapper {
            height: 24px;
            display: flex;
            justify-content: center;
        }
    }
</style>
