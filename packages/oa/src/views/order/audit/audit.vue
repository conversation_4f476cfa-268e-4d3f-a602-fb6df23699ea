<script setup lang="ts">
import { ApprovalTicketAPI } from '@/api/approval-ticket-api';
import { PageParams } from '@/common/model/page-params';
import OaList from '@/components/oa-list.vue';
import { convert2OrderSummaryItem as convert2OrderSummaryItemByAirPharmacy } from '@/views/air-pharmacy/model/adapter';
import { convert2OrderSummaryItem } from '@/views/order/model/adapter';
import { useRouter } from 'vue-router';
import { OrderType } from '@/views/air-pharmacy/model/model';

const router = useRouter();
function toApproval(item: any) {
    router.push({
        // 产品说两种订单不会出现在同一个人的提交里面，以防万一进行区分
        name: item.orderInfo.orderType === OrderType.PREVENTION_PRESCRIPTION_USING ? '@air-pharmacy/detail' : '@order/detail',
        params: {
            id: item.orderInfo.id,
        },
        query: {
            orderType: item.orderInfo.orderType,
        },
    });
}

async function fetchAuditedList(pageParams: PageParams) {
    console.log('fetchAuditedList', pageParams);
    const {
        rows = [],
        limit,
        offset,
        total,
        keyword,
    } = await ApprovalTicketAPI.findApprovalTicketListByAuditedByMeUsingGET(pageParams.keyword, pageParams.limit, pageParams.offset);

    return {
        rows: rows.map(row => (
            // 产品说两种订单不会出现在同一个人的提交里面，以防万一进行区分
            row.approvalId === OrderType.PREVENTION_PRESCRIPTION_USING
                ? convert2OrderSummaryItemByAirPharmacy(row)
                : convert2OrderSummaryItem(row)
        )),
        limit,
        offset,
        total,
    };
}

async function fetchCcList(pageParams: PageParams) {
    console.log('fetchCcList', pageParams);
    const {
        rows = [],
        limit,
        offset,
        total,
        keyword,
    } = await ApprovalTicketAPI.findApprovalTicketListByCcToMeUsingGET(pageParams.keyword, pageParams.limit, pageParams.offset);

    return {
        rows: rows.map(row => (
            // 产品说两种订单不会出现在同一个人的提交里面，以防万一进行区分
            row.approvalId === OrderType.PREVENTION_PRESCRIPTION_USING
                ? convert2OrderSummaryItemByAirPharmacy(row)
                : convert2OrderSummaryItem(row)
        )),
        limit,
        offset,
        total,
    };
}

async function fetchWaitingAuditList(pageParams: PageParams) {
    console.log('fetchCcList', pageParams);
    const {
        rows = [],
        limit,
        offset,
        total,
        keyword,
    } = await ApprovalTicketAPI.findApprovalTicketListByWaitingAuditUsingGET(pageParams.keyword, pageParams.limit, pageParams.offset);

    return {
        rows: rows.map(row => (
            // 产品说两种订单不会出现在同一个人的提交里面，以防万一进行区分
            row.approvalId === OrderType.PREVENTION_PRESCRIPTION_USING
                ? convert2OrderSummaryItemByAirPharmacy(row)
                : convert2OrderSummaryItem(row)
        )),
        limit,
        offset,
        total,
    };
}

</script>
<template>
    <van-tabs sticky :offset-top="16">
        <van-tab title="待审批" class="audit-list-wrapper">
            <oa-list :data-provider="fetchWaitingAuditList" searchable>
                <template #default="{item}">
                    <component
                        :is="item.component"
                        :order-info="item.orderInfo"
                        @click="toApproval(item)"
                    ></component>
                </template>
            </oa-list>
        </van-tab>
        <van-tab title="已审批" class="audit-list-wrapper">
            <oa-list :data-provider="fetchAuditedList" searchable>
                <template #default="{item}">
                    <component
                        :is="item.component"
                        :order-info="item.orderInfo"
                        @click="toApproval(item)"
                    ></component>
                </template>
            </oa-list>
        </van-tab>
        <van-tab title="抄送我" class="audit-list-wrapper">
            <oa-list :data-provider="fetchCcList" searchable>
                <template #default="{item}">
                    <component
                        :is="item.component"
                        :order-info="item.orderInfo"
                        @click="toApproval(item)"
                    ></component>
                </template>
            </oa-list>
        </van-tab>
    </van-tabs>
</template>
<style lang="scss">
    .audit-list-wrapper {
        padding-top: 8px;

        .oa-card-wrapper + .oa-card-wrapper {
            margin-top: 8px;
        }
    }
</style>
