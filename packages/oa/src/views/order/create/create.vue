<script lang="ts" setup>
import { ApprovalTicketAPI } from '@/api/approval-ticket-api';
import { ClinicNodeType, HisType, getHisTypeDisplayName } from '@/utils/clinic';
import { formatEdition, formatMoney } from '@/utils/format';
import { sleep } from '@/utils/utils';
import { H5Form } from '@/vendor/x-form';
import { Form } from '@/vendor/x-form/core/form';
import { useOrderStore } from '@/views/order/store';
import { onBeforeUnmount, reactive, ref } from 'vue';
import { OrderType } from '../model/model';
import { Toast } from 'vant';
import { useRouter } from 'vue-router';
import {
    createPostDataForChain,
    createPostDataForSingle,
    createPostDataForSub,
    createPostDataForTrialToNormalize,
    formSchema,
} from '@/views/order/model/schema';

let formControl: Form;

const router = useRouter();

const orderStore = useOrderStore();

const oldClinicName = ref('');

const oldHisType = ref<number>();

const getClinicNodeTypeLabel = (formData: any) => {
    const options = [
        { label: '新开单店', value: ClinicNodeType.SINGLE },
        { label: '新开连锁', value: ClinicNodeType.CHAIN_ADMIN },
        { label: '新开子店', value: ClinicNodeType.CHAIN_SUB },
        { label: '试用门店转正', value: ClinicNodeType.TRIAL_TO_NORMALIZE },
    ];
    return options.find((item: any) => item.value === formData.clinicType)?.label;
};

const actionData = reactive<any>({
    action: <any>{}, formData: <any>{}, form: <any>{}, valid: false,
});
const showSubmitDialog = ref(false);
async function handleAction({ action, formData, valid, form }: any) {
    if (action.name === 'submit') {
        if (!valid) {
            Toast.fail('请完善表单内容');
            return;
        }
        actionData.action = action || {};
        actionData.formData = formData || {};
        actionData.form = form || {};
        actionData.valid = valid;
        showSubmitDialog.value = true;
    }
}

async function handleSubmitDialogAction() {
    const { action, formData, valid, form } = actionData;

    if (action.name === 'submit') {
        console.log('submit', formData, valid);
        if (!valid) {
            Toast.fail('请完善表单内容');
            return;
        }
        if (formData.clinicType === ClinicNodeType.CHAIN_SUB && formData.editionId === '220') {
            Toast.fail('眼视光管家连锁子店暂不支持标准版！');
            return;
        }
        action.loading = true;

        Toast.loading('门店创建中');

        let createResponse: any = null;
        let orderType = OrderType.CREATE_CHAIN;

        if (formData.clinicType === ClinicNodeType.CHAIN_ADMIN) {
            // 新开总部
            try {
                const postData = createPostDataForChain(formData);
                createResponse = await ApprovalTicketAPI.createApprovalTicketCreateChainUsingPOST(postData);
                orderStore.setAddressRegion(postData.addressRegion);
            } catch (e: any) {
                Toast.fail('创建门店失败-' + e.message);
                action.loading = false;
            }
        } else if (formData.clinicType === ClinicNodeType.CHAIN_SUB) {
            // 新开子店
            try {
                formData.hisType = form.getField('chainId')?.getBundle()?.hisType;
                const postData = createPostDataForSub(formData);
                createResponse = await ApprovalTicketAPI.createApprovalTicketCreateClinicUsingPOST(postData);
                orderType = OrderType.CREATE_CLINIC;
                orderStore.setReceiveAccountId(formData.receiveAccountId);
                orderStore.setAddressRegion(postData.addressRegion);
            } catch (e: any) {
                Toast.fail('创建门店失败-' + e.message);
                action.loading = false;
            }
        } else if (formData.clinicType === ClinicNodeType.SINGLE) {
            // 新开单店
            try {
                const postData = createPostDataForSingle(formData);
                createResponse = await ApprovalTicketAPI.createApprovalTicketCreateClinicUsingPOST(postData);
                orderType = OrderType.CREATE_CLINIC;
                orderStore.setReceiveAccountId(formData.receiveAccountId);
                orderStore.setAddressRegion(postData.addressRegion);
            } catch (e: any) {
                Toast.fail('创建门店失败-' + e.message);
                action.loading = false;
            }
        } else if (formData.clinicType === ClinicNodeType.TRIAL_TO_NORMALIZE) {
            // 试用门店转正
            try {
                formData.hisType = form.getField('clinicId')?.getBundle()?.hisType || form.formData.hisType;
                const postData = createPostDataForTrialToNormalize(formData);
                createResponse = await ApprovalTicketAPI.createApprovalTicketTransTrialClinicUsingPOST(postData);
                orderType = OrderType.TRIAL_TO_NORMALIZE;
                orderStore.setReceiveAccountId(formData.receiveAccountId);
            } catch (e: any) {
                Toast.fail('创建门店失败-' + e.message);
                action.loading = false;
            }
        }

        if (createResponse?.id) {
            await sleep(2000);
            showSubmitDialog.value = false;
            Toast.success('创建门店成功');
            action.loading = false;
            await router.replace({
                name: orderType === OrderType.CREATE_CHAIN ? '@order/detail' : '@order/pay',
                params: {
                    id: createResponse.id,
                },
                query: {
                    orderType,
                },
            });
        }
    }
}

onBeforeUnmount(() => {
    if (orderStore.currentFormData) {
        orderStore.clearEditFormData();
    }
});

function handlePrepared(form: Form) {
    formControl = form;
}

function updateCrmGOrganOptions() {
    if (orderStore.currentFormData?.crmOrganId) {
        formControl?.setQuerySchemaOptions('crmOrganId', [
            {
                label: orderStore.currentFormData?.crmOrganName,
                value: orderStore.currentFormData?.crmOrganId,
            },
        ]);
    }
}

function checkNameAndType(data: any) {
    if (data.clinicName !== oldClinicName.value || data.hisType !== oldHisType.value) {
        oldClinicName.value = data.clinicName;
        const nameCheckDentistryList = ['口腔', '牙科'];
        const nameCheckOphthalmologyList = ['眼科', '视光'];
        const organInfo = formControl?.getField('chainId')?.getBundle();
        const clinicType = data.clinicType;
        let type = clinicType === ClinicNodeType.CHAIN_SUB ? organInfo?.hisType : data.hisType;
        const name = clinicType !== ClinicNodeType.CHAIN_ADMIN ? data.clinicName : data.chainName;
        oldHisType.value = type;
        if ((type || type === 0) && type !== HisType.DENTISTRY) {
            nameCheckDentistryList.forEach((item: any) => {
                name.includes(item) && Toast.fail('请确认产品类型是否正确');
            });
        }
        if ((type || type === 0) && type !== HisType.OPHTHALMOLOGY) {
            nameCheckOphthalmologyList.forEach((item: any) => {
                name.includes(item) && Toast.fail('请确认产品类型是否正确');
            });
        }
    }
}

function updateSearchKeyword() {
    if (orderStore.currentFormData?.clinicType === ClinicNodeType.TRIAL_TO_NORMALIZE) {
        formSchema.properties.clinicId.componentProps.searchKeyword = orderStore.currentFormData?.clinicName;
    }
}

function handleChange({ formData: data, property }: any) {
    updateCrmGOrganOptions();
    if (orderStore.currentFormData?.clinicType !== ClinicNodeType.TRIAL_TO_NORMALIZE) {
        checkNameAndType(data);
    }
    updateSearchKeyword();
    console.log('handleChange', property, data);
}

function getClinicName() {
    if (actionData.formData.clinicType === ClinicNodeType.TRIAL_TO_NORMALIZE) {
        const clinicBundle = formControl?.getField('clinicId')?.getBundle() || {};
        return clinicBundle.name || actionData.formData.clinicName || '';
    } if (actionData.formData.clinicType !== ClinicNodeType.CHAIN_ADMIN) {
        return actionData.formData.clinicName || '';
    }
    return actionData.formData.chainName;
}
</script>
<template>
    <h5-form
        :schema="formSchema"
        :data="orderStore.currentFormData"
        @action="handleAction"
        @prepared="handlePrepared"
        @change="handleChange"
    ></h5-form>
    <van-dialog
        v-model:show="showSubmitDialog"
        title=""
        show-cancel-button
        class="submit-dialog"
        @confirm="handleSubmitDialogAction"
    >
        <p
            v-if="getHisTypeDisplayName(actionData.formData)"
            class="submit-dialog-label"
        >
            产品<span class="submit-dialog-text">{{ getHisTypeDisplayName(actionData.formData) }}</span>
        </p>
        <p class="submit-dialog-label">
            门店
            <span class="submit-dialog-text">
                {{ getClinicName() }}
            </span>
        </p>
        <p class="submit-dialog-label">类型<span class="submit-dialog-text">{{ getClinicNodeTypeLabel(actionData.formData) }}</span></p>
        <p
            v-if="actionData.formData.clinicType !== ClinicNodeType.CHAIN_ADMIN"
            class="submit-dialog-label"
        >
            版本<span class="submit-dialog-text">{{ formatEdition(actionData.formData.editionId) }}</span>
        </p>
        <p
            v-if="actionData.formData.clinicType !== ClinicNodeType.CHAIN_ADMIN"
            class="submit-dialog-label"
        >
            金额<span class="submit-dialog-text">{{ formatMoney(actionData.formData.receivableFee) }}</span>
        </p>
    </van-dialog>
</template>

<style lang="scss">
.submit-dialog {
    .van-dialog__content {
        padding: 24px 24px 24px 36px;
    }

    .submit-dialog-label {
        color: #989b98;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .submit-dialog-text {
        color: #000;
        padding-left: 24px;
        display: inline-block;
        width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
</style>
