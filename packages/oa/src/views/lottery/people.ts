import { LuckyItem } from '@/views/lottery/lottery-store';

export const people: LuckyItem[] = [{
    name: '杨明昆',
    type: 1,
},
{
    name: '粟霄',
    type: 1,
},
{
    name: '吴成芳',
    type: 1,
},
{
    name: '徐天翔',
    type: 1,
},
{
    name: '莫沙',
    type: 0,
},
{
    name: '王红梅',
    type: 1,
},
{
    name: '苏可',
    type: 0,
},
{
    name: '樊豫',
    type: 1,
},
{
    name: '袁列诚',
    type: 1,
},
{
    name: '吕鹏',
    type: 1,
},
{
    name: '毛永强',
    type: 1,
},
{
    name: '陈益',
    type: 1,
},
{
    name: '徐颖',
    type: 1,
},
{
    name: '唐启涛',
    type: 1,
},
{
    name: '夏荣隆',
    type: 1,
},
{
    name: '鲁文文',
    type: 1,
},
{
    name: '赵锦',
    type: 1,
},
{
    name: '何飞',
    type: 1,
},
{
    name: '王彬鉴',
    type: 1,
},
{
    name: '陈磊',
    type: 1,
},
{
    name: '王晓东',
    type: 1,
},
{
    name: '冯思遥',
    type: 1,
},
{
    name: '黄利州',
    type: 1,
},
{
    name: '蔡先伟',
    type: 1,
},
{
    name: '邓毅',
    type: 1,
},
{
    name: '胡恒昶',
    type: 1,
},
{
    name: '邓杰',
    type: 1,
},
{
    name: '钟鑫',
    type: 1,
},
{
    name: '刘强',
    type: 1,
},
{
    name: '李鹏程',
    type: 1,
},
{
    name: '韩鑫',
    type: 1,
},
{
    name: '李燕秋',
    type: 1,
},
{
    name: '李琪',
    type: 1,
},
{
    name: '李龙彬',
    type: 1,
},
{
    name: '汪虹宇',
    type: 1,
},
{
    name: '蒲涛',
    type: 1,
},
{
    name: '常真豪',
    type: 1,
},
{
    name: '徐彩云',
    type: 1,
},
{
    name: '蒋羽佳',
    type: 1,
},
{
    name: '吴文娟',
    type: 1,
},
{
    name: '王刚',
    type: 1,
},
{
    name: '侯宇森',
    type: 1,
},
{
    name: '肖磊',
    type: 1,
},
{
    name: '李超群',
    type: 1,
},
{
    name: '李炜',
    type: 1,
},
{
    name: '彭静',
    type: 1,
},
{
    name: '卜海洋',
    type: 1,
},
{
    name: '殷明',
    type: 1,
},
{
    name: '赵旭东',
    type: 1,
},
{
    name: '唐威',
    type: 1,
},
{
    name: '李云涛',
    type: 1,
},
{
    name: '蒲小坤',
    type: 1,
},
{
    name: '杨攀',
    type: 1,
},
{
    name: '黄小华',
    type: 1,
},
{
    name: '章渊',
    type: 1,
},
{
    name: '姚兴武',
    type: 1,
},
{
    name: '李朝平',
    type: 1,
},
{
    name: '包欢欢',
    type: 1,
},
{
    name: '龚雪梅',
    type: 1,
},
{
    name: '文武',
    type: 1,
},
{
    name: '邵阳',
    type: 1,
},
{
    name: '曾琴',
    type: 1,
},
{
    name: '王成金',
    type: 1,
},
{
    name: '胡宇锐',
    type: 1,
},
{
    name: '蒲思静',
    type: 1,
},
{
    name: '石登元',
    type: 1,
},
{
    name: '冉毅',
    type: 1,
},
{
    name: '王攀',
    type: 1,
},
{
    name: '蒋晓风',
    type: 1,
},
{
    name: '彭松',
    type: 1,
},
{
    name: '赵小枫',
    type: 1,
},
{
    name: '崔益慧',
    type: 1,
},
{
    name: '尹啸扬',
    type: 1,
},
{
    name: '李康',
    type: 1,
},
{
    name: '姜磊',
    type: 1,
},
{
    name: '王祖鑫',
    type: 1,
},
{
    name: '张帅',
    type: 1,
},
{
    name: '卜海燕',
    type: 1,
},
{
    name: '杜洪亮',
    type: 1,
},
{
    name: '许赵飞',
    type: 1,
},
{
    name: '梁张琦',
    type: 1,
},
{
    name: '覃思源',
    type: 1,
},
{
    name: '王宇森',
    type: 1,
},
{
    name: '王丹',
    type: 1,
},
{
    name: '孙晓凤',
    type: 1,
},
{
    name: '刘子弋',
    type: 1,
},
{
    name: '孙新佳',
    type: 1,
},
{
    name: '文文',
    type: 1,
},
{
    name: '罗贤亮',
    type: 1,
},
{
    name: '王旭',
    type: 1,
},
{
    name: '张筠',
    type: 1,
},
{
    name: '罗涛',
    type: 1,
},
{
    name: '吴鑫睿',
    type: 0,
},
{
    name: '蒋世纪',
    type: 1,
},
{
    name: '李洲宇',
    type: 1,
},
{
    name: '曾龙',
    type: 1,
},
{
    name: '袁洪',
    type: 1,
},
{
    name: '赵金彤',
    type: 1,
},
{
    name: '龙俊宇',
    type: 0,
},
{
    name: '刘建花',
    type: 0,
},
{
    name: '何宇翔',
    type: 0,
},
{
    name: '宋世耀',
    type: 0,
},
{
    name: '辜余洪',
    type: 0,
},
{
    name: '赵家',
    type: 0,
},
{
    name: '周莎莎',
    type: 0,
},
{
    name: '张劲松',
    type: 0,
},
{
    name: '唐丽娟',
    type: 0,
},
{
    name: '李娟娟',
    type: 0,
},
{
    name: '陈叶亮',
    type: 0,
},
{
    name: '陈美洁',
    type: 0,
},
{
    name: '门丽佳',
    type: 0,
},
{
    name: '杨智棚',
    type: 0,
},
{
    name: '沈文清',
    type: 0,
},
{
    name: '赵一璠',
    type: 0,
},
{
    name: '周鹏',
    type: 0,
},
{
    name: '李桃',
    type: 0,
},
{
    name: '裴熊',
    type: 0,
},
{
    name: '何龙英',
    type: 0,
},
{
    name: '郭文斌',
    type: 0,
},
{
    name: '李臻屹',
    type: 0,
},
{
    name: '师晓霞',
    type: 0,
},
{
    name: '杨惠姣',
    type: 0,
},
{
    name: '何平',
    type: 0,
},
{
    name: '陈昆鹏',
    type: 0,
},
{
    name: '廖鸿',
    type: 0,
},
{
    name: '阳兰',
    type: 0,
},
{
    name: '杨继刚',
    type: 0,
},
{
    name: '谷显丹',
    type: 0,
},
{
    name: '李晓冬',
    type: 0,
},
{
    name: '彭敏候',
    type: 1,
},
{
    name: '李天文',
    type: 1,
},
{
    name: '王英杰',
    type: 1,
},
{
    name: '阎重阳',
    type: 1,
},
{
    name: '胡宇豪',
    type: 1,
},
{
    name: '唐周周',
    type: 1,
},
{
    name: '许明欢',
    type: 1,
},
{
    name: '王玉婷',
    type: 1,
},
{
    name: '敖铖宸',
    type: 1,
},
{
    name: '王棚',
    type: 1,
},
{
    name: '冯杨',
    type: 1,
},
{
    name: '李睿',
    type: 1,
},
{
    name: '张瑶',
    type: 1,
},
{
    name: '余林峰',
    type: 1,
},
{
    name: '朱敏',
    type: 1,
},
{
    name: '许龙',
    type: 1,
},
{
    name: '何雷',
    type: 1,
},
{
    name: '苏晨',
    type: 1,
},
{
    name: '潘越',
    type: 1,
},
{
    name: '王诗怡',
    type: 1,
},
{
    name: '罗蒙',
    type: 1,
},
{
    name: '唐玮',
    type: 1,
},
{
    name: '王江晖',
    type: 1,
},
{
    name: '云飞',
    type: 1,
},
{
    name: '王起红',
    type: 1,
},
{
    name: '潘澳',
    type: 1,
},
{
    name: '唐斌',
    type: 0,
},
{
    name: '张世玲',
    type: 0,
},
{
    name: '洪银先',
    type: 0,
},
{
    name: '蒋东角',
    type: 0,
},
{
    name: '杨苗',
    type: 0,
},
{
    name: '董健',
    type: 0,
},
{
    name: '邹秋月',
    type: 0,
},
{
    name: '李翔',
    type: 0,
},
{
    name: '贺高峰',
    type: 2,
},
{
    name: '林波',
    type: 2,
},
{
    name: '林和龙',
    type: 2,
},
{
    name: '杨彦飞',
    type: 2,
},
{
    name: '邵东全',
    type: 2,
},
{
    name: '张雪娇',
    type: 2,
},
{
    name: '周姝姝',
    type: 2,
},
{
    name: '蔡茂琳',
    type: 2,
},
];
