import ZoneTaskIndex from './zone/index.vue';

// ... 其他导入保持不变

export default [
    {
        path: 'deployment',
        name: '@deployment',
        component: Entry,
        meta: {
            name: '发布平台',
            icon: 'partly-cloudy',
            roles: PermissionDeploymentViewSet,
        },
        redirect: {
            path: '/deployment/entry',
        },
        children: [
            // ... 其他路由保持不变
            {
                path: 'zone-task-list',
                name: '@deployment/zone-task-list',
                component: ZoneTaskIndex,
                alias: '/deployment/zone-task-entry',
                meta: {
                    name: '可用区放量列表',
                    roles: PermissionDeploymentViewSet,
                },
            },
            // ... 其他路由保持不变
        ],
    },
];