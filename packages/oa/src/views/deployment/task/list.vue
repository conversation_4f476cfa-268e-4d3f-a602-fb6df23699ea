<script setup lang="ts">
import { DeploymentApi } from '@/api/deployment-api';
import router from '@/router';
import { useDeploymentStore } from '@/views/deployment/store';
import { Edit, Grid } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { DeployTaskType } from '@/views/deployment/model';
import { computed, onMounted, reactive, watch } from 'vue';
import { formatTime } from '@/utils/format';
import { useUserStore } from '@/store/user';

const props = defineProps({
    type: {
        type: Number,
        default: DeployTaskType.DEPLOY,
    },
});

const vmData = reactive({
    rows: <any>[],
    page: 1,
    pageSize: 10,
    total: 0,
    dialogVisible: false,
    saveBtnLoading: false,
    deleteBtnLoading: false,
    editRow: <any>null,
    preview: {
        paramsList: [{
            key: '',
            value: '',
            init: true,
        }],
        result: <any>null,
    },
});

const deploymentStore = useDeploymentStore();
const userStore = useUserStore();
const isEdit = computed(() => vmData.editRow?.id);
const isQaTask = computed(() => props.type === DeployTaskType.QA);

watch(vmData.preview.paramsList, (val) => {
    const lastParam = val[val.length - 1];
    // 不是初始状态，说明编辑过，此时 push 一行
    if (!lastParam.init) {
        vmData.preview.paramsList.push({
            key: '',
            value: '',
            init: true,
        });
    }
}, {
    deep: true,
});

async function fetchData() {
    const offset = ((vmData.page - 1) * vmData.pageSize || 0).toString();
    const limit = vmData.pageSize.toString();
    const { rows, total } = await DeploymentApi.getApiLowCodeDeploymentDeployTask(props.type, offset, limit);
    vmData.rows = rows;
    vmData.total = total;
}

function handleEditClick(row: any) {
    vmData.editRow = row;
    vmData.dialogVisible = true;
}

async function handleDeleteClick(row: any) {
    let updatedStatus = 0;
    if (row.status === 0) {
        updatedStatus = 90;
    }
    vmData.deleteBtnLoading = true;
    try {
        await DeploymentApi.patchApiLowCodeDeploymentDeployTaskByIdUpdateStatus({
            type: props.type,
            status: updatedStatus,
        }, row.id);
    } catch (e: any) {
        ElMessage.error('修改状态失败: ' + e.message);
    } finally {
        vmData.deleteBtnLoading = false;
    }
    fetchData();
}

async function handleSaveClick() {
    if (!vmData.editRow.name) {
        ElMessage.warning('请填写标题');
        return;
    }
    vmData.saveBtnLoading = true;

    if (isEdit.value) {
        try {
            await DeploymentApi.patchApiLowCodeDeploymentDeployTaskById({
                name: vmData.editRow.name,
            }, vmData.editRow.id);
        } catch (e: any) {
            vmData.saveBtnLoading = false;
            ElMessage.error('修改班车失败: ' + e.message);
            return;
        }
    } else {
        try {
            await DeploymentApi.postApiLowCodeDeploymentDeployTask({
                type: props.type,
                name: vmData.editRow.name,
            });
        } catch (e: any) {
            vmData.saveBtnLoading = false;
            ElMessage.error('开车失败: ' + e.message);
            return;
        }
    }

    vmData.saveBtnLoading = false;
    vmData.dialogVisible = false;
    fetchData();
}

async function handleTaskClick(row: any) {
    console.log('handleTaskClick', row);
    router.push({
        name: isQaTask.value ? '@deployment/qa' : '@deployment/task',
        params: {
            id: row.id,
        },
    });
}

function handleTaskZoneClick(row: any) {
    router.push({
        name: isQaTask.value ? '@deployment/qa' : '@deployment/task-old',
        params: {
            id: row.id,
        },
    });
}

function handleCreateClick() {
    vmData.editRow = {
        title: '',
        name: '',
        content: '',
    };
    vmData.dialogVisible = true;
}

function handleDialogClose() {
    vmData.preview.result = '';
}

onMounted(() => {
    fetchData();
});
</script>
<template>
    <el-row style="margin-bottom: 12px;">
        <el-col>
            <el-button type="primary" :disabled="!deploymentStore.hasPermissionPublish" @click="handleCreateClick">新增{{ isQaTask ? '提测' : '' }}</el-button>
        </el-col>
    </el-row>

    <el-table
        :data="vmData.rows"
        style="width: 100%;"
        @row-click="handleTaskClick"
    >
        <el-table-column
            prop="name"
            label="班车名"
        >
        </el-table-column>
        <el-table-column
            prop="type"
            label="班车状态"
            width="90"
        >
            <template #default="{row}">{{ row.status === 0 ? '打开' : '关闭' }}</template>
        </el-table-column>
        <el-table-column
            prop="created"
            label="创建时间"
            width="180"
        >
            <template #default="{row}">{{ formatTime(row.created) }}</template>
        </el-table-column>
        <el-table-column
            prop="operation"
            label="操作"
            width="200"
        >
            <template #default="{row}">
                <el-button
                    size="small"
                    type="primary"
                    :icon="Edit"
                    :disabled="!deploymentStore.hasPermissionPublish"
                    @click.stop="handleEditClick(row)"
                ></el-button>
                <el-popconfirm
                    :title="row.status === 0 ? '确定停止班车吗？' : '确定恢复班车吗'"
                    confirm-button-text="确认"
                    cancel-button-text="取消"
                    confirm-button-type="text"
                    @confirm="handleDeleteClick(row)"
                >
                    <template #reference>
                        <el-button
                            size="small"
                            :type="row.status === 0 ? 'danger' : 'success'"
                            :disabled="!deploymentStore.hasPermissionChangeTaskStatus"
                            @click.stop
                        >
                            {{ row.status === 0 ? '停车' : '发车' }}
                        </el-button>
                    </template>
                </el-popconfirm>

                <el-button
                    v-if="userStore.userInfo.userId === 'bubble'"
                    size="small"
                    type="ghost"
                    :icon="Grid"
                    @click.stop="handleTaskZoneClick(row)"
                ></el-button>
            </template>
        </el-table-column>
        <template #append>
            <el-pagination
                v-model:current-page="vmData.page"
                v-model:page-size="vmData.pageSize"
                class="deployment-pagination"
                background
                layout="prev, pager, next, sizes, total "
                :total="vmData.total"
                @current-change="fetchData"
                @size-change="fetchData"
            >
                <template #total>
                    <span>共 {{ vmData.total }} 条</span>
                </template>
            </el-pagination>
        </template>
    </el-table>

    <el-dialog
        v-model="vmData.dialogVisible"
        :title="isEdit ? '编辑班车' : '新增班车'"
        width="50%"
        @close="handleDialogClose"
    >
        <div class="script-editor">
            <el-form label-width="140px" label-position="left">
                <el-form-item label="班车名称">
                    <el-input v-model="vmData.editRow.name"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <template #footer class="dialog-footer">
            <el-button @click="vmData.dialogVisible = false">取 消</el-button>
            <el-button type="primary" :loading="vmData.saveBtnLoading" @click="handleSaveClick">保存</el-button>
        </template>
    </el-dialog>
</template>
<style lang="scss">
.deployment-pagination {
    display: flex;
    justify-content: flex-end;
    margin: 12px;

    .el-pagination__sizes {
        padding-left: 12px;
    }
}
</style>
