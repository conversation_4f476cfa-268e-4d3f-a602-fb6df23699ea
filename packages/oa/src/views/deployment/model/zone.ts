export enum ZoneTaskStatus {
    NEW = 0,
    EFFECTIVE = 10,
    FINISHED = 20,
    STOP = 30,
}

export enum ZoneTaskDetailStatus {
    NEW = 0,
    EFFECTIVE = 10,
    FINISHED = 20,
    STOP = 30,
}

const regionLabelMap: Record<number, string> = {
    1: '上海',
    2: '杭州',
};

const envLabelMap: Record<number, string> = {
    0: '预发布',
    1: '灰度',
    2: '正式',
};

const zoneLabelMap: Record<string, string> = {
    primary: '主用区',
    standby: '备用区',
};

export function formatZoneTaskStatus(status?: number) {
    return {
        [ZoneTaskStatus.NEW]: '未生效',
        [ZoneTaskStatus.EFFECTIVE]: '执行中',
        [ZoneTaskStatus.FINISHED]: '已完成',
        [ZoneTaskStatus.STOP]: '已暂停',
    }[status ?? ZoneTaskStatus.NEW] || '未知状态';
}

export function formatZoneTaskDetailStatus(status?: number) {
    return {
        [ZoneTaskDetailStatus.NEW]: '待执行',
        [ZoneTaskDetailStatus.EFFECTIVE]: '执行中',
        [ZoneTaskDetailStatus.FINISHED]: '已完成',
        [ZoneTaskDetailStatus.STOP]: '已暂停',
    }[status ?? ZoneTaskDetailStatus.NEW] || '未知状态';
}

export function formatRegionLabel(regionId?: number) {
    if (typeof regionId !== 'number') {
        return '未知分区';
    }
    return regionLabelMap[regionId] || `分区 ${regionId}`;
}

export function formatEnvLabel(env?: number) {
    if (typeof env !== 'number') {
        return '未知环境';
    }
    return envLabelMap[env] || `环境 ${env}`;
}

export function formatZoneLabel(zone?: string) {
    if (!zone) {
        return '未知可用区';
    }
    return zoneLabelMap[zone] || zone;
}

export const zoneOptions = [
    { label: '主用区', value: 'primary' },
    { label: '备用区', value: 'standby' },
];

export const envOptions = [
    { label: '正式', value: 0 },
    { label: '灰度', value: 1 },
];

export const regionOptions = [
    { label: '上海', value: 1 },
    { label: '杭州', value: 2 },
];

export function isTaskActive(status?: number) {
    return status === ZoneTaskStatus.EFFECTIVE;
}

export function canResumeTask(status?: number) {
    return status === ZoneTaskStatus.STOP || status === ZoneTaskStatus.NEW;
}

export function canPauseTask(status?: number) {
    return status === ZoneTaskStatus.EFFECTIVE;
}

export function isTaskFinished(status?: number) {
    return status === ZoneTaskStatus.FINISHED;
}
