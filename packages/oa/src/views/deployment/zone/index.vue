<template>
  <div class="zone-task-container">
    <div class="header">
      <h2>分区放量任务管理</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        创建任务
      </el-button>
    </div>

    <!-- 任务列表 -->
    <el-table :data="taskList" v-loading="loading">
      <el-table-column prop="name" label="任务名称" />
      <el-table-column prop="regionId" label="分区" :formatter="formatRegion" />
      <el-table-column prop="env" label="环境" :formatter="formatEnv" />
      <el-table-column prop="fromZone" label="从分区" :formatter="formatZone" />
      <el-table-column prop="toZone" label="到分区" :formatter="formatZone" />
      <el-table-column prop="status" label="状态" :formatter="formatStatus" />
      <el-table-column prop="created" label="创建时间" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="viewDetail(row)">详情</el-button>
          <el-button
            v-if="canPauseTask(row.status)"
            size="small"
            type="warning"
            @click="pauseTask(row)"
          >
            暂停
          </el-button>
          <el-button
            v-if="canResumeTask(row.status)"
            size="small"
            type="success"
            @click="resumeTask(row)"
          >
            启动
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建任务对话框 -->
    <CreateTaskDialog
      v-model="showCreateDialog"
      @success="loadTaskList"
    />

    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model="showDetailDialog"
      :task-id="selectedTaskId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { HighlyAvailableAPI } from '@/api/highly-available-api';
import { regionOptions, envOptions, zoneOptions, canPauseTask, canResumeTask } from '../model/zone';
import CreateTaskDialog from './CreateTaskDialog.vue';
import TaskDetailDialog from './TaskDetailDialog.vue';

const taskList = ref<AbcAPI.CreateZoneTaskReq[]>([]);
const loading = ref(false);
const showCreateDialog = ref(false);
const showDetailDialog = ref(false);
const selectedTaskId = ref('');

const loadTaskList = async () => {
  loading.value = true;
  try {
    const res = await HighlyAvailableAPI.listZoneTasksUsingGET();
    console.log('API 返回数据:', res);
    console.log('res.data:', res.data);
    console.log('res.data.rows:', res.data?.rows);
    taskList.value = res.data?.rows || [];
    console.log('taskList.value:', taskList.value);
  } catch (error) {
    console.error('获取任务列表失败:', error);
  } finally {
    loading.value = false;
  }
};

const formatRegion = (row: AbcAPI.CreateZoneTaskReq) => {
  return regionOptions.find(item => item.value === row.regionId)?.label || row.regionId;
};

const formatEnv = (row: AbcAPI.CreateZoneTaskReq) => {
  return envOptions.find(item => item.value === row.env)?.label || row.env;
};

const formatZone = (row: AbcAPI.CreateZoneTaskReq, column: any, cellValue: string) => {
  return zoneOptions.find(item => item.value === cellValue)?.label || cellValue;
};

const formatStatus = (row: AbcAPI.CreateZoneTaskReq) => {
  const statusMap = {
    10: '进行中',
    20: '完成',
    30: '手动停止'
  };
  return statusMap[row.status as keyof typeof statusMap] || row.status;
};

const viewDetail = (row: AbcAPI.CreateZoneTaskReq) => {
  selectedTaskId.value = String(row.id);
  showDetailDialog.value = true;
};

const pauseTask = async (row: AbcAPI.CreateZoneTaskReq) => {
  await HighlyAvailableAPI.updateZoneTaskStatusUsingPUT(30, String(row.id));
  loadTaskList();
};

const resumeTask = async (row: AbcAPI.CreateZoneTaskReq) => {
  await HighlyAvailableAPI.updateZoneTaskStatusUsingPUT(10, String(row.id));
  loadTaskList();
};

onMounted(() => {
  loadTaskList();
});
</script>