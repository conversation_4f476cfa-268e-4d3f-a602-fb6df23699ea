<template>
    <el-dialog v-model="visible" title="任务详情" width="800px">
        <div v-if="taskDetail" v-loading="loading">
            <el-descriptions :column="2" border>
                <el-descriptions-item label="任务名称">
                    {{ taskDetail.name }}
                </el-descriptions-item>
                <el-descriptions-item label="任务ID">
                    {{ taskDetail.id }}
                </el-descriptions-item>
                <el-descriptions-item label="分区">
                    {{ formatRegion(taskDetail.regionId) }}
                </el-descriptions-item>
                <el-descriptions-item label="环境">
                    {{ formatEnv(taskDetail.env) }}
                </el-descriptions-item>
                <el-descriptions-item label="从分区">
                    {{ formatZone(taskDetail.fromZone) }}
                </el-descriptions-item>
                <el-descriptions-item label="到分区">
                    {{ formatZone(taskDetail.toZone) }}
                </el-descriptions-item>
                <el-descriptions-item label="状态">
                    {{ formatStatus(taskDetail.status) }}
                </el-descriptions-item>
                <el-descriptions-item label="创建人">
                    {{ taskDetail.createdBy }}
                </el-descriptions-item>
                <el-descriptions-item label="创建时间">
                    {{ taskDetail.created }}
                </el-descriptions-item>
                <el-descriptions-item label="完成时间">
                    {{ taskDetail.finished || '-' }}
                </el-descriptions-item>
            </el-descriptions>

            <!-- 灰度步骤列表 -->
            <div v-if="taskDetail.list?.length" class="mt-4">
                <h3>灰度步骤</h3>
                <el-table :data="taskDetail.list">
                    <el-table-column prop="step" label="步骤" width="80" />
                    <el-table-column prop="name" label="步骤名称" />
                    <el-table-column prop="regionCount" label="放量比例(%)" width="120" />
                    <el-table-column prop="started" label="开始时间" width="180" />
                    <el-table-column prop="status" label="状态" width="100">
                        <template #default="{ row }">
                            <el-tag :type="getStatusType(row.status)">
                                {{ formatDetailStatus(row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { HighlyAvailableAPI } from '@/api/highly-available-api';
import { regionOptions, envOptions, zoneOptions } from '../model/zone';

const props = defineProps<{
  modelValue: boolean;
  taskId: string;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
});

const taskDetail = ref<AbcAPI.CreateZoneTaskReq | null>(null);
const loading = ref(false);

const loadTaskDetail = async () => {
    if (!props.taskId) return;

    loading.value = true;
    try {
        const res = await HighlyAvailableAPI.getZoneTaskUsingGET(props.taskId);
        taskDetail.value = res.data;
    } finally {
        loading.value = false;
    }
};

const formatRegion = (regionId: number) => regionOptions.find(item => item.value === regionId)?.label || regionId;

const formatEnv = (env: number) => envOptions.find(item => item.value === env)?.label || env;

const formatZone = (zone: string) => zoneOptions.find(item => item.value === zone)?.label || zone;

const formatStatus = (status: number) => {
    const statusMap = {
        10: '进行中',
        20: '完成',
        30: '手动停止',
    };
    return statusMap[status as keyof typeof statusMap] || status;
};

const formatDetailStatus = (status: number) => {
    const statusMap = {
        0: '未开始',
        10: '进行中',
        20: '完成',
    };
    return statusMap[status as keyof typeof statusMap] || status;
};

const getStatusType = (status: number) => {
    const typeMap = {
        0: 'info',
        10: 'warning',
        20: 'success',
    };
    return typeMap[status as keyof typeof typeMap] || 'info';
};

watch(() => props.taskId, () => {
    if (props.taskId && visible.value) {
        loadTaskDetail();
    }
});

watch(visible, (val) => {
    if (val && props.taskId) {
        loadTaskDetail();
    }
});
</script>