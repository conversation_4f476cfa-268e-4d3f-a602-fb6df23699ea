
<template>
    <el-dialog v-model="visible" title="创建放量任务" width="800px">
        <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="100px"
        >
            <el-form-item label="任务名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入任务名称" />
            </el-form-item>

            <el-form-item label="分区" prop="regionId">
                <el-select v-model="form.regionId" placeholder="请选择分区">
                    <el-option
                        v-for="item in regionOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>

            <el-form-item label="环境" prop="env">
                <el-select v-model="form.env" placeholder="请选择环境">
                    <el-option
                        v-for="item in envOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>

            <el-form-item label="从分区" prop="fromZone">
                <el-select v-model="form.fromZone" placeholder="请选择从分区">
                    <el-option
                        v-for="item in zoneOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>

            <el-form-item label="到分区" prop="toZone">
                <el-select v-model="form.toZone" placeholder="请选择到分区">
                    <el-option
                        v-for="item in zoneOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>

            <!-- 放量明细配置 -->
            <el-divider content-position="left">放量明细配置</el-divider>

            <div class="detail-list">
                <el-table
                    :data="form.details"
                    border
                    stripe
                    size="small"
                    class="detail-table"
                    :row-key="(row) => row.step"
                >
                    <el-table-column label="步骤名称" min-width="200"  align="left">
                        <template #default="{ row, $index }">
                            <el-form-item
                                :prop="`details.${$index}.name`"
                                :rules="detailRules.name"
                                class="detail-form-item"
                            >
                                <el-input v-model="row.name" placeholder="请输入步骤名称" />
                            </el-form-item>
                        </template>
                    </el-table-column>

                    <el-table-column label="放量比例(%)" width="250" align="left">
                        <template #default="{ row, $index }">
                            <el-form-item
                                :prop="`details.${$index}.percentage`"
                                :rules="detailRules.percentage"
                                class="detail-form-item"
                            >
                                <el-input
                                    v-model.number="row.percentage"
                                    type="number"
                                    placeholder="请输入比例"
                                />
                            </el-form-item>
                        </template>
                    </el-table-column>

                    <el-table-column label="开始时间" min-width="350">
                        <template #default="{ row, $index }">
                            <el-form-item
                                :prop="`details.${$index}.started`"
                                :rules="detailRules.started"
                                class="detail-form-item"
                            >
                                <el-date-picker
                                    v-model="row.started"
                                    type="datetime"
                                    placeholder="请选择开始时间"
                                    style="width: 100%"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                />
                            </el-form-item>
                        </template>
                    </el-table-column>

                    <el-table-column label="操作" width="110" align="center">
                        <template #default="{ $index }">
                            <el-button
                                type="danger"
                                size="small"
                                @click="removeDetail($index)"
                                :disabled="form.details.length <= 1"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <el-button type="primary" plain @click="addDetail" class="detail-add-btn">
                    添加放量步骤
                </el-button>
            </div>
        </el-form>

        <template #footer>
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
                创建
            </el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { HighlyAvailableAPI } from '@/api/highly-available-api';
import { regionOptions, envOptions, zoneOptions } from '../model/zone';

interface DetailItem {
    step: string;
    name: string;
    percentage: number;
    started: string;
}

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  success: [];
}>();

const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
});

const formRef = ref();
const submitting = ref(false);

const form = ref({
    name: '',
    regionId: undefined as number | undefined,
    env: undefined as number | undefined,
    fromZone: '',
    toZone: '',
    details: [
        { step: '1', name: '', percentage: 10, started: '' }
    ] as DetailItem[]
});

const rules = {
    name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
    regionId: [{ required: true, message: '请选择分区', trigger: 'change' }],
    env: [{ required: true, message: '请选择环境', trigger: 'change' }],
    fromZone: [{ required: true, message: '请选择从分区', trigger: 'change' }],
    toZone: [{ required: true, message: '请选择到分区', trigger: 'change' }],
};

const detailRules = {
    step: [{ required: true, message: '请输入步骤', trigger: 'blur' }],
    name: [{ required: true, message: '请输入步骤名称', trigger: 'blur' }],
    percentage: [
        { required: true, message: '请输入放量百分比', trigger: 'blur' },
        {
            validator: (rule: any, value: any, callback: any) => {
                if (value === '' || value === null || value === undefined) {
                    callback();
                    return;
                }
                if (!/^\d+(\.\d+)?$/.test(value) || value <= 0 || value > 100) {
                    callback(new Error('请输入1-100之间的数字'));
                    return;
                }
                callback();
            },
            trigger: 'blur'
        }
    ],
    started: [{ required: true, message: '请选择开始时间', trigger: 'change' }]
};

const addDetail = () => {
    const nextStep = String(form.value.details.length + 1);
    form.value.details.push({
        step: nextStep,
        name: '',
        percentage: 0,
        started: ''
    });
};

const removeDetail = (index: number) => {
    form.value.details.splice(index, 1);
    // 重新排序步骤号
    form.value.details.forEach((detail, idx) => {
        detail.step = String(idx + 1);
    });
};

const handleSubmit = async () => {
    if (!formRef.value) return;

    const valid = await formRef.value.validate();
    if (!valid) return;

    // 验证放量百分比总和必须等于100%
    const totalPercentage = form.value.details.reduce((sum, detail) => sum + (detail.percentage || 0), 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
        ElMessage.warning(`放量百分比总和必须等于100%，当前为${totalPercentage}%`);
        return;
    }

    submitting.value = true;
    try {
        // 转换前端数据结构为后端期望的格式
        const zoneTaskDetails = form.value.details.map((detail, index) => {
            // 将本地时间转换为ISO 8601格式（带时区）
            let startedISO = '';
            if (detail.started) {
                try {
                    const date = new Date(detail.started);
                    // 检查日期是否有效
                    if (!isNaN(date.getTime())) {
                        startedISO = date.toISOString();
                    }
                } catch (error) {
                    console.warn('无效的时间格式:', detail.started);
                }
            }

            return {
                countType: 1, // 比例类型
                name: detail.name,
                regionCount: detail.percentage,
                started: startedISO,
                // 以下字段由后端生成
                id: 0,
                created: '',
                finished: '',
                realRegionCount: 0,
                status: 0
            } as AbcAPI.ZoneTaskDetail;
        });

        const taskData = {
            name: form.value.name,
            regionId: form.value.regionId!,
            env: form.value.env!,
            fromZone: form.value.fromZone,
            toZone: form.value.toZone,
            list: zoneTaskDetails,
            // 以下字段由后端生成
            id: 0,
            created: '',
            createdBy: '',
            finished: '',
            status: 0
        } as AbcAPI.CreateZoneTaskReq;

        await HighlyAvailableAPI.createZoneTaskUsingPOST(taskData);
        ElMessage.success('任务创建成功');
        visible.value = false;
        emit('success');
        resetForm();
    } catch (error) {
        console.error('创建任务失败:', error);
        ElMessage.error('创建任务失败，请重试');
    } finally {
        submitting.value = false;
    }
};

const resetForm = () => {
    form.value = {
        name: '',
        regionId: undefined,
        env: undefined,
        fromZone: '',
        toZone: '',
        details: [
            { step: '1', name: '', percentage: 10, started: '' }
        ]
    };
    formRef.value?.resetFields();
};
</script>

<style scoped>
.detail-list {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-table {
  border-radius: 6px;
  overflow: hidden;
}

.detail-table :deep(.el-table__header-wrapper th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
}

.detail-table :deep(.el-table__cell) {
  padding: 12px 16px;
}

.detail-form-item {
  margin-bottom: 0;
}

.detail-form-item :deep(.el-form-item__error) {
  position: static;
  padding-top: 4px;
}

.detail-add-btn {
  width: 100%;
  align-self: stretch;
}
</style>
