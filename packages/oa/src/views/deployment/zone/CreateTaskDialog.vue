<template>
    <el-dialog v-model="visible" title="创建放量任务" width="800px">
        <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="100px"
        >
            <el-form-item label="任务名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入任务名称" />
            </el-form-item>
      
            <el-form-item label="分区" prop="regionId">
                <el-select v-model="form.regionId" placeholder="请选择分区">
                    <el-option 
                        v-for="item in regionOptions" 
                        :key="item.value" 
                        :label="item.label" 
                        :value="item.value" 
                    />
                </el-select>
            </el-form-item>

            <el-form-item label="环境" prop="env">
                <el-select v-model="form.env" placeholder="请选择环境">
                    <el-option 
                        v-for="item in envOptions" 
                        :key="item.value" 
                        :label="item.label" 
                        :value="item.value" 
                    />
                </el-select>
            </el-form-item>

            <el-form-item label="从分区" prop="fromZone">
                <el-select v-model="form.fromZone" placeholder="请选择从分区">
                    <el-option 
                        v-for="item in zoneOptions" 
                        :key="item.value" 
                        :label="item.label" 
                        :value="item.value" 
                    />
                </el-select>
            </el-form-item>

            <el-form-item label="到分区" prop="toZone">
                <el-select v-model="form.toZone" placeholder="请选择到分区">
                    <el-option 
                        v-for="item in zoneOptions" 
                        :key="item.value" 
                        :label="item.label" 
                        :value="item.value" 
                    />
                </el-select>
            </el-form-item>

            <!-- 放量明细配置 -->
            <el-divider content-position="left">放量明细配置</el-divider>
      
            <div class="detail-list">
                <div v-for="(detail, index) in form.details" :key="index" class="detail-item">
                    <el-row :gutter="16">
                        <el-col :span="8">
                            <el-form-item :prop="`details.${index}.description`" :rules="detailRules.description">
                                <el-input v-model="detail.description" placeholder="描述" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item :prop="`details.${index}.percentage`" :rules="detailRules.percentage">
                                <el-input-number 
                                    v-model="detail.percentage" 
                                    :min="0" 
                                    :max="100" 
                                    placeholder="放量百分比"
                                    style="width: 100%"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-button 
                                type="danger" 
                                size="small" 
                                @click="removeDetail(index)"
                                :disabled="form.details.length <= 1"
                            >
                                删除
                            </el-button>
                        </el-col>
                    </el-row>
                </div>
                
                <el-button type="primary" plain @click="addDetail" style="width: 100%; margin-top: 10px;">
                    添加放量步骤
                </el-button>
            </div>
        </el-form>

        <template #footer>
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
                创建
            </el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { HighlyAvailableAPI } from '@/api/highly-available-api';
import { regionOptions, envOptions, zoneOptions } from '../model/zone';

interface DetailItem {
    step: string;
    description: string;
    percentage: number;
}

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  success: [];
}>();

const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
});

const formRef = ref();
const submitting = ref(false);

const form = ref({
    name: '',
    regionId: undefined as number | undefined,
    env: undefined as number | undefined,
    fromZone: '',
    toZone: '',
    details: [
        { step: '1', description: '', percentage: 10 }
    ] as DetailItem[]
});

const rules = {
    name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
    regionId: [{ required: true, message: '请选择分区', trigger: 'change' }],
    env: [{ required: true, message: '请选择环境', trigger: 'change' }],
    fromZone: [{ required: true, message: '请选择从分区', trigger: 'change' }],
    toZone: [{ required: true, message: '请选择到分区', trigger: 'change' }],
};

const detailRules = {
    step: [{ required: true, message: '请输入步骤', trigger: 'blur' }],
    description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
    percentage: [
        { required: true, message: '请输入放量百分比', trigger: 'blur' },
        { 
            validator: (rule: any, value: any, callback: any) => {
                if (value === '' || value === null || value === undefined) {
                    callback();
                    return;
                }
                if (!/^\d+(\.\d+)?$/.test(value) || value <= 0 || value > 100) {
                    callback(new Error('请输入1-100之间的数字'));
                    return;
                }
                callback();
            },
            trigger: 'blur'
        }
    ]
};

const addDetail = () => {
    const nextStep = String(form.value.details.length + 1);
    form.value.details.push({
        step: nextStep,
        description: '',
        percentage: 0
    });
};

const removeDetail = (index: number) => {
    form.value.details.splice(index, 1);
    // 重新排序步骤号
    form.value.details.forEach((detail, idx) => {
        detail.step = String(idx + 1);
    });
};

const handleSubmit = async () => {
    if (!formRef.value) return;
  
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 验证放量百分比总和必须等于100%
    const totalPercentage = form.value.details.reduce((sum, detail) => sum + (detail.percentage || 0), 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
        ElMessage.warning(`放量百分比总和必须等于100%，当前为${totalPercentage}%`);
        return;
    }

    submitting.value = true;
    try {
        const taskData = {
            ...form.value,
            list: form.value.details
        };
        await HighlyAvailableAPI.createZoneTaskUsingPOST(taskData as AbcAPI.CreateZoneTaskReq);
        visible.value = false;
        emit('success');
        resetForm();
    } finally {
        submitting.value = false;
    }
};

const resetForm = () => {
    form.value = {
        name: '',
        regionId: undefined,
        env: undefined,
        fromZone: '',
        toZone: '',
        details: [
            { step: '1', description: '', percentage: 10 }
        ]
    };
    formRef.value?.resetFields();
};
</script>

<style scoped>
.detail-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}
</style>
