<script setup lang="ts">
import OaList from '@/components/oa-list.vue';
import { convert2OrderSummaryItem } from '@/views/air-pharmacy/model/adapter';
import { convert2OrderSummaryItem as convert2OrderSummaryItemByOrder } from '@/views/order/model/adapter';
import { useRouter } from 'vue-router';
import { ApprovalTicketAPI } from '@/api/approval-ticket-api';
import { OrderType } from '@/views/air-pharmacy/model/model';

async function fetchList(pageParams: any) {
    const listResponse = await ApprovalTicketAPI.findApprovalTicketListByCreatedByMeUsingGET('', pageParams.limit, pageParams.offset);
    let {
        limit,
        offset,
        total,
        rows = [],
    } = listResponse;
    return {
        rows: rows.map(row => (
            // 产品说两种订单不会出现在同一个人的提交里面，以防万一进行区分
            row.approvalId === OrderType.PREVENTION_PRESCRIPTION_USING
                ? convert2OrderSummaryItem(row)
                : convert2OrderSummaryItemByOrder(row)
        )),
        limit,
        offset,
        total,
    };
}

const router = useRouter();
function toSubmited(item: any) {
    router.push({
        // 产品说两种订单不会出现在同一个人的提交里面，以防万一进行区分
        name: item.orderInfo.orderType === OrderType.PREVENTION_PRESCRIPTION_USING ? '@air-pharmacy/detail' : '@order/detail',
        params: {
            id: item.orderInfo.id,
        },
        query: {
            orderType: item.orderInfo.orderType,
        },
    });
}
</script>
<template>
    <div class="order-submit-list-wrapper">
        <oa-list
            :data-provider="fetchList"
            :page-size="7"
        >
            <template #default="{item}">
                <component
                    :is="item.component"
                    :order-info="item.orderInfo"
                    @click="toSubmited(item)"
                ></component>
            </template>
        </oa-list>
    </div>
</template>

<style lang="scss">
.order-submit-list-wrapper {
    height: 100%;

    .oa-card-wrapper + .oa-card-wrapper {
        margin-top: 8px;
    }
}
</style>
