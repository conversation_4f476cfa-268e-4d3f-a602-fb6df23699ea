import { DiseaseDetailOptions } from '@/views/disease/model';

export const DISEASE_CATEGORY_TYPE_OPTIONS: readonly any[] = Object.freeze([
    {
        value: 0,
        label: '西医疾病',
    },
    {
        value: 1,
        label: '中医疾病',
    },
]);

export const DISEASE_DETAIL_OPTIONS: readonly DiseaseDetailOptions[] = Object.freeze([
    {
        title: '疾病名称',
        list: [
            {
                title: '病名',
                key: 'name',
                required: true,
            },
            {
                title: '别名',
                key: 'alias',
            },
            {
                title: '拼音',
                key: 'pyName',
            },
        ],
    },
    {
        title: '疾病分类',
        list: [
            {
                title: '一级分类',
                key: 'type',
                slot: 'type',
                required: true,
            },
            {
                title: '二级分类',
                key: 'parentCategoryId',
                slot: 'parentCategoryId',
                required: true,
            },
        ],
    },
]);