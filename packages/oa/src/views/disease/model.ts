export interface DiseaseData {
   id?: string;
   name?: string;
   pyName?: string;
   alias?: string;
   type?: number;
   parentCategoryId?: string;
   list?: {
      id?: string;
      diseaseId?: string;
      content?: string;
      category?: string;
      sort?: number;
      tagId?: string;
      tagName?: string;
      isAdd?: number
   }[]
}

export interface DiseaseTag {
   id?: string
   label?: string
   value?: string
}

export interface Tree {
   id: string;
   category: string;
   type: string;
   sort?: number;
   parentCategoryId?: string;
   children?: Tree[]
}

export interface DiseaseDetailOptions {
   title: string
   list: {
      title: string
      key: keyof DiseaseData
      slot?:string
      required?: boolean
   }[]
}