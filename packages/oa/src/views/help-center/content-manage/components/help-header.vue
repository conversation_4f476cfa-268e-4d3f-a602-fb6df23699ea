<script lang="ts" setup>
import { inject, onMounted, ref } from 'vue';
import { QuestionQuery } from '@/views/help-center/model';

interface LinkItem {
    value: string
    link: string
}

const links = ref<LinkItem[]>([]);

const loadAll = () => [
    { value: 'vue', link: 'https://github.com/vuejs/vue' },
    { value: 'element', link: 'https://github.com/ElemeFE/element' },
    { value: 'cooking', link: 'https://github.com/ElemeFE/cooking' },
    { value: 'mint-ui', link: 'https://github.com/ElemeFE/mint-ui' },
    { value: 'vuex', link: 'https://github.com/vuejs/vuex' },
    { value: 'vue-router', link: 'https://github.com/vuejs/vue-router' },
    { value: 'babel', link: 'https://github.com/babel/babel' },
];

let timeout: NodeJS.Timeout;

const handleSelect = (item: LinkItem) => {
    console.log(item);
};

const getQuestionList: any = inject('getQuestionList');
const paginationOptions: any = inject('paginationOptions');
/**
 * @Description: 搜索热词
 * <AUTHOR> Cai
 * @date 2022/09/21 15:01:53
*/
const searchHotWord = () => {
    getQuestionList();
};
onMounted(() => {
    links.value = loadAll();
});
const query: any = inject('query');
const roleList: any = inject('roleList');
const funcList: any = inject('funcList');
</script>

<template>
    <div class="help-header">
        <div class="select-container-item">
            <span style="margin-right: 10px;">角色</span>
            <el-select
                v-model="query.roleIds"
                multiple
                collapse-tags
                filterable
                clearable
                placeholder="请选择角色"
                style="width: 240px;"
            >
                <el-option
                    v-for="item in roleList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                />
            </el-select>
        </div>
        <div class="select-container-item">
            <span style="margin-right: 10px;">功能</span>
            <el-select
                v-model="query.funcIds"
                multiple
                collapse-tags
                filterable
                clearable
                placeholder="请选择功能"
                style="width: 240px;"
            >
                <el-option
                    v-for="item in funcList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                />
            </el-select>
        </div>

        <div class="select-container-item">
            <el-input
                v-model="query.keyword"
                placeholder="请输入关键词"
            >
                <template #append>
                    <el-button @click="searchHotWord">搜索</el-button>
                </template>
            </el-input>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.help-header {
    width: 100%;
    height: 100%;
    background-color: white;
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
    padding: 10px 20px;
    border-radius: 6px;

    .select-container-item {
        margin-right: 50px;
    }
}
</style>