<template>
    <div class="pacs-device">
        <el-card :style="{ marginBottom: '24px' }" shadow="never">
            <div class="header">
                <div class="left">
                    <el-select v-model="filterParams.connectStatus" placeholder="全部状态" clearable>
                        <el-option
                            v-for="(d,i) in AllStatus"
                            :key="i"
                            :label="d.label"
                            :value="d.value"
                        />
                    </el-select>

                    <el-input
                        v-model="filterParams.keyword"
                        :style="{ width: '200px' }"
                        prefix-icon="search"
                        placeholder="名称、型号、UUID"
                    >
                    </el-input>
                </div>
            </div>
        </el-card>
        <el-card shadow="never">
            <div class="opt">
                <el-button type="primary" @click="controller.handleUpdateDevice">新增仪器</el-button>
            </div>
            <el-table 
                v-loading="loading" 
                :data="deviceData"
                style="width: 100%;"
                @cell-click="controller.handleTableCellClick"
            >
                <el-table-column prop="name" label="仪器名称" />
                <el-table-column prop="manufacture" label="品牌" />
                <el-table-column prop="model" label="型号" />
                <el-table-column prop="deviceType" label="类型">
                    <template #default="{ row }">
                        {{ formatDeviceType(row) }}
                    </template>
                </el-table-column>
                <el-table-column prop="shortId" label="设备ID" />
                <el-table-column prop="deviceUuid" label="UUID" />
                <el-table-column prop="connectStatus" label="状态">
                    <template #default="{ row }">
                        <el-tag :type="row.connectStatus === 0 ? 'info' : row.connectStatus === 10 ? 'success' : ''">
                            {{ StatusMap[row.connectStatus] }}
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination 
                    :current-page="paginationProps.page"
                    background
                    layout="prev, pager, next" 
                    :total="paginationProps.total" 
                    @update:current-page="controller.handlePageChange"
                />
            </div>
        </el-card>
        <update-device-dialog 
            v-if="dialogVisible" 
            :id="editDeviceId"
            v-model="dialogVisible"
            @refresh="controller.initPageData"
        ></update-device-dialog>
    </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue';
import { AllStatus, DeviceType, StatusMap } from './constant';
import { useController } from './controller';
import { useModel } from './model';
import UpdateDeviceDialog from './update-device-dialog/index.vue';

const model = useModel();

const controller = useController(model);

const { 
    deviceData, 
    filterParams, 
    paginationProps, 
    dialogVisible, 
    loading,
    editDeviceId,
} = model;

onMounted(controller.initPageData);

watch(filterParams, () => {
    model.setPagination({
        page: 1,
    });
    controller.throttleGetDeviceData();
});

const formatDeviceType = (row: any) => {
    const { isEye, deviceType } = row;
    if (isEye) return '眼科检查';

    const item = DeviceType.find((o) => o.value === deviceType);
    if (item) {
        return item.label;
    }

    return '_';
};

</script>

<style scoped lang="scss">
    .pacs-device {
        :deep(.el-card__body) {
            padding: 24px;
        }
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: center;

        .left {
            display: flex;
            flex: 1;

            :deep(.el-select) {
                margin-right: 24px;
            }
        }

        .right {
            margin-left: 24px;
        }
    }

    .opt {
        margin-bottom: 24px;
    }

    .pagination {
        display: flex;
        justify-content: flex-end;
        margin-top: 24px;
    }
</style>