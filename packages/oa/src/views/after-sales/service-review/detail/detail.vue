<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue';
import { AfterSalesApi } from '@/api/after-sales-api';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { AUDIT_LEVEL_OPTIONS } from '@/views/after-sales/service-review/constant';

import ChatRecord from '@/components/chat-record/chat-record.vue';
import _ from 'lodash';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    isAudit: {
        type: Boolean,
        default: false,
    },
    id: {
        type: String,
        default: '',
    },
    conversationId: {
        type: String,
        default: '',
    },
    servicerId: {
        type: String,
        default: '',
    },
    currentRows: {
        type: Object,
        default: () => { },
    },
});
const emit = defineEmits(['update:visible', 'refresh']);
const dialogVisible = computed({
    get() {
        return props.visible;
    },
    set(val) {
        emit('update:visible', val);
    },
});
const timelineData = ref<any[]>([]);
const timelineLoading = ref(false);
const auditLevelOptions = AUDIT_LEVEL_OPTIONS;
const loading = ref(false);
/**
 * 获取会话记录详情
 */
const getConversationDetail = async () => {
    let res: any = {};
    loading.value = true;
    try {
        res = await AfterSalesApi.getApiLowCodeAfterSalesDetail(props.conversationId);
    } catch (e: any) {
        loading.value = false;
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
    }
    if (res && res.rows) {
        return {
            rows: res.rows,
        };
    }
    loading.value = false;
};
/**
 * 关闭弹窗
 */
const onHandleCancel = () => {
    const wrapper = document.querySelector('.timeline-wrapper');
    if (wrapper) {
        wrapper.scrollTop = 0;
    }

    form.value.auditLevels = [];
    dialogVisible.value = false;
};

const form = ref<any>({});
watch(() => props.currentRows, (newVal: any) => {
    form.value = _.cloneDeep(newVal);
    form.value.auditLevels = _.cloneDeep(newVal.auditResults);
}, { immediate: true, deep: true });
const ruleFormRef = ref<FormInstance>();
const rules = reactive<FormRules>({
    auditLevels: [{ required: true, message: '请选择审核等级！', trigger: 'blur', type: 'array' }],
});
/**
 * 审核会话记录
 */
const onHandleAudit = async () => {
    if (!props.isAudit || !ruleFormRef.value) {
        dialogVisible.value = false;
        return;
    }
    await ruleFormRef.value.validate(async (valid, fields) => {
        if (valid) {
            let res: any = {};
            timelineLoading.value = true;
            const params = {
                id: props.id,
                conversationId: props.conversationId,
                servicerId: props.servicerId,
                auditResults: form.value.auditLevels || [],
                auditRemark: form.value.auditRemark,
            };
            try {
                res = await AfterSalesApi.postApiLowCodeAfterSalesAudit(params);
            } catch (e: any) {
                timelineLoading.value = false;
                ElMessage({
                    type: 'error',
                    message: e.message || e,
                });
            }
            if (res && res.id) {
                ElMessage({
                    type: 'success',
                    message: '会话记录审核成功！',
                });
                emit('refresh');
                onHandleCancel();
            }
            timelineLoading.value = false;
        } else {
            console.log('error submit!', fields);
        }
    });
};
</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        :title="`售后服务接待${isAudit ? '质量审核' : '详情'}`"
        width="60%"
        :before-close="onHandleCancel"
        custom-class="after-sales-detail-wrapper"
        :close-on-press-escape="false"
        destroy-on-close
    >
        <el-form
            v-if="isAudit"
            ref="ruleFormRef"
            :model="form"
            :rules="rules"
            class="audit-wrapper"
            label-position="top"
        >
            <el-form-item label="请对本次售后服务进行审核" prop="auditLevels">
                <el-checkbox-group v-model="form.auditLevels" class="flex-column">
                    <el-checkbox v-for="level in auditLevelOptions" :key="level.value" :label="level.value">{{ level.label }}</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="审核备注" prop="auditRemark">
                <el-input
                    v-model="form.auditRemark"
                    type="textarea"
                    :autosize="{ minRows: 1 }"
                ></el-input>
            </el-form-item>
        </el-form>
        <el-divider v-if="isAudit" direction="vertical" style="height: auto;" />
        <chat-record
            :data-provider="getConversationDetail"
            :is-paginate="false"
            :infinite-scroll-delay="300"
            @dialogCancel="onHandleCancel"
        ></chat-record>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="onHandleCancel">取消</el-button>
                <el-button
                    v-if="isAudit"
                    type="primary"
                    @click="onHandleAudit"
                >提交</el-button>
            </span>
        </template>
    </el-dialog>
</template>
<style lang="scss">
.after-sales-detail-wrapper {
    .el-dialog__body {
        display: flex;
        overflow-x: auto;
    }

    .audit-wrapper {
        flex: .4;
        margin-bottom: 8px;
        margin-right: 8px;
    }

    .chat-record-wrapper {
        flex: auto;
    }

    .identity-line {
        display: flex;
        align-items: center;

        > span {
            padding-left: 8px;
        }
    }

    .conversation-line {
        margin-top: 8px;
    }

    .el-dialog__footer,
    .dialog-footer {
        text-align: left;
    }
}

.display-none {
    display: none;
}
</style>