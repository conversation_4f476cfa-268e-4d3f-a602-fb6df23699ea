<template>
    <div class="category-list">
        <!-- 第二个图表 (当有 parentId 时显示) -->
        <div v-if="selectedFirstCategory && secondChartData.length > 0" class="chart-section">
            <h3 class="section-title">二级分类榜</h3>
            <div ref="secondChartContainer" class="second-chart-container"></div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading">
            <van-loading size="24px">加载中...</van-loading>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && categoryList.length === 0" class="empty-state">
            <div class="empty-text">暂无分类数据</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import { Icon as VanIcon, Loading as VanLoading, Toast } from 'vant';
import * as echarts from 'echarts';
import { CsSummaryAPI } from '@/api/cs-summary-api';
import { useGroupSessionMobile } from '../hooks/useGroupSessionMobile';

const { changeComponent, componentParams } = useGroupSessionMobile();

// 接收父组件传递的props
const props = defineProps<{
    endDate: string;
    beginDate: string;
}>();

// 数据状态
const loading = ref(false);
const categoryList = ref<any[]>([]);
const secondChartData = ref<any[]>([]);
const selectedFirstCategory = ref<string>('');

// 图表实例
let secondChartInstance: echarts.ECharts | null = null;
const secondChartContainer = ref<HTMLElement>();

// 获取分类数据
const getCategoryData = async () => {
    loading.value = true;
    try {
        const topNum = 100;
        // 如果有 parentId，获取二级分类数据
        const parentId = componentParams.value?.parentId;
        
        const res: any = await CsSummaryAPI.getChatTagTopUsingGET(
            topNum, 
            props.endDate, 
            parentId,
            props.beginDate,
        );
    
        categoryList.value = res?.rows?.map((item: any) => ({
            id: item.tagId,
            name: item.tagName,
            count: item.count,
        })) || [];
        
        // 如果有 parentId，说明这是二级分类，需要显示图表
        if (parentId) {
            selectedFirstCategory.value = parentId;
            secondChartData.value = res?.rows?.map((item: any) => ({
                ...item,
                name: item.tagName,
                value: item.count,
            })) || [];
            
            nextTick(() => {
                initSecondChart();
            });
        }
    } catch (error) {
        Toast.fail('获取分类数据失败');
    } finally {
        loading.value = false;
    }
};

// 跳转到会话列表
const goToSessionList = (item: any) => {
    // 传递分类信息给会话列表页面
    changeComponent('SessionList', false, item);
};

// 初始化第二个图表
const initSecondChart = () => {
    if (!secondChartContainer.value) return;
    
    if (secondChartInstance) {
        secondChartInstance.dispose();
    }
    
    secondChartInstance = echarts.init(secondChartContainer.value);
    
    const option = !secondChartData.value || secondChartData.value.length === 0
        ? {
            graphic: {
                elements: [{
                    type: 'text',
                    style: {
                        text: '暂无数据',
                        font: '14px sans-serif',
                        fill: '#999',
                    },
                    left: 'center',
                    top: 'center',
                }],
            },
        }
        : {
            grid: {
                left: '0',
                right: '10%',
                top: '3%',
                bottom: '18%',
                containLabel: true,
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                },
            },
            xAxis: {
                type: 'value',
                axisLabel: {
                    fontSize: 12,
                },
                minInterval: 1,
            },
            yAxis: {
                type: 'category',
                data: secondChartData.value.map(item => item.name),
                axisLabel: {
                    fontSize: 12,
                    interval: 0,
                },
                inverse: true,
            },
            series: [{
                name: '咨询数量',
                type: 'bar',
                data: secondChartData.value.map(item => item.value),
                itemStyle: {
                    color: '#67C23A',
                },
                label: {
                    show: true,
                    position: 'right',
                    fontSize: 10,
                    formatter: '{c}',
                },
            }],
        };
    
    secondChartInstance.setOption(option);
    
    // 只有在有数据时才添加点击事件
    if (secondChartData.value && secondChartData.value.length > 0) {
        // 添加点击事件
        secondChartInstance.on('click', (params: any) => {
            const clickedData = secondChartData.value[params.dataIndex];
            if (clickedData) {
                goToSessionList(clickedData);
            }
        });
    }
};

// 监听日期变化，重新加载数据
watch(() => [props.endDate, props.beginDate], () => {
    getCategoryData();
}, { immediate: false });

onMounted(() => {
    getCategoryData();
});
</script>

<style scoped lang="scss">
.category-list {
    background-color: #f5f5f5;
    height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;

    .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        padding: 16px 16px 8px;
        margin: 0;
    }

    .chart-section {
        flex: 1;
        background-color: #fff;

        .second-chart-container {
            height: 400px;
            padding: 16px;
            box-shadow: unset;
        }
    }

    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 0;
        background-color: #fff;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        background-color: #fff;

        .empty-text {
            font-size: 14px;
            color: #999;
            margin-top: 16px;
        }
    }
}
</style>