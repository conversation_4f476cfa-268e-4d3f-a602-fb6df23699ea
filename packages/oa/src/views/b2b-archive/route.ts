import { RouteRecordRaw } from 'vue-router';

import B2BArchive from './index.vue';
import { PermissionB2BArchive } from '@/views/b2b-archive/permission';

export default [
    {
        path: 'b2b-archive',
        name: '@B2BArchive',
        component: B2BArchive,
        meta: {
            isEntry: true,
            name: 'B2B沟通存档',
            icon: 'Files',
            roles: PermissionB2BArchive,
        },
    },
] as RouteRecordRaw[];
