<script setup lang="ts">
import { querySchema, tableSchema } from './schema';
import QueryTable from '@/components/query-table.vue';
import { ref } from 'vue';
import { Form } from '@/vendor/x-form/core/form';
import { useFormat } from '@/composables/date';
import { useRoute } from 'vue-router';
import CompleteDistribution from './complete-distribution.vue';
import { ClinicActivityAPI } from '@/api/clinic-activity-api';
import { ElMessage } from 'element-plus/es';
import { REWARD_ENUM } from '../constant';

let formControl: Form;

const route = useRoute();
/**
 * 筛选项已就绪
 * @param form 筛选项值
 */
const handlePrepared = (form: Form) => {
    formControl = form;
};

const visibleCompleteDialog = ref(false);
const currentRows = ref<any>({});

/**
 * 查看详情
 * @param column
 * @param row
 */
const onHandleView = (column: any, row: any) => {
    currentRows.value = row || {};
    visibleCompleteDialog.value = true;
};
const sourceClueId = ref(null);
/**
 * @description: 录入编辑线索ID
 * @date: 2023-10-11 10:37:57
 * @author: Horace
 * @param null:
 * @return
*/
const handleClueIdEdit = (row: any) => {
    sourceClueId.value = row.clueId;
    row.isEditClueId = true;
};
const handleClueIdCancel = (row: any) => {
    row.clueId = sourceClueId.value;
    sourceClueId.value = null;
    row.isEditClueId = false;
};
const handleClueIdSubmit = async (row: any) => {
    let res: any = {};
    try {
        res = await ClinicActivityAPI.updateRewardsClueUsingPUT(row.editionOrderId, {
            clinicId: row.clinicId,
            clueId: row.clueId,
        });
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    if (res && res.code) {
        row.isEditClueId = false;
    }
};

const queryTableRef = ref();
/**
 * 刷新数据
 */
const refreshData = () => {
    if (!queryTableRef.value) {
        return;
    }
    queryTableRef.value.debounceQuery(formControl.formData);
};

</script>
<template>
    <div class="clue-management-wrapper">
        <query-table
            ref="queryTableRef"
            :table-schema="tableSchema"
            :query-schema="querySchema"
            @prepared="handlePrepared"
        >
            <template #operate="{column, row}">
                <el-space v-if="row.isEditClueId">
                    <el-button @click="handleClueIdCancel(row)">取消</el-button>
                    <el-button type="primary" @click="handleClueIdSubmit(row)">确认</el-button>
                </el-space>
                <el-space v-else>
                    <el-link v-if="!row.rewardStatus" type="primary" @click="handleClueIdEdit( row)">
                        {{ row.clueId ?'编辑线索ID': '录入线索ID' }}
                    </el-link>
                    <el-link
                        v-if="+row.rewardStatus !== REWARD_ENUM.HAS_BEEN_ISSUED && row.clueId "
                        type="primary"
                        @click="onHandleView(column, row)"
                    >
                        完成奖励发放
                    </el-link>
                    <template v-if="+row.rewardStatus === REWARD_ENUM.HAS_BEEN_ISSUED && row.attachments">
                        <el-image
                            v-for="image in row.attachments"
                            :key="image.url"
                            :src="image.url"
                            :initial-index="1"
                            :preview-teleported="true"
                            :preview-src-list="[image.url]"
                            style="width: 50px; height: 50px;"
                        ></el-image>
                    </template>
                </el-space>
            </template>
            <template #formatTime="{column, row}">
                {{ useFormat(row[column.prop],'YYYY-MM-DD HH:mm:ss') }}
            </template>
            <template #rewardStatus="{column, row}">
                <span v-if="row.clueId && row.rewardStatus" :style="{color: +row[column.prop] === REWARD_ENUM.HAS_BEEN_ISSUED ? '#000' : '#00baad' }">
                    {{
                        `${+row[column.prop] === REWARD_ENUM.HAS_BEEN_ISSUED ? '已发放' : '发放中'} `
                    }}
                </span>
            </template>
            <template #clueId="{column, row}">
                <span v-if="!row.isEditClueId">{{ row[column.prop] || '' }}</span>
                <el-input
                    v-else
                    v-model="row[column.prop]"
                    style="width: 120px;"
                ></el-input>
            </template>
        </query-table>
    </div>
    <complete-distribution
        v-model:visible="visibleCompleteDialog"
        :current-rows="currentRows"
        @refresh="refreshData"
    >
    </complete-distribution>
</template>
<style lang="scss">
.clue-management-wrapper {
    .el-col {
        margin-right: 16px;

        .el-form-item {
            margin-bottom: 0;
        }

        .el-select-v2 {
            width: 100%;
        }
    }
}
</style>
