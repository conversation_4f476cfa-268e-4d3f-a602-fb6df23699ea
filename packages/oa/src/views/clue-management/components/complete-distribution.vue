<script setup lang="ts">
import { computed, ref } from 'vue';
import OSSUtil from '@/utils/oss';
import OaUploader from '@/components/oa-uploader.vue';
import { ClinicActivityAPI } from '@/api/clinic-activity-api';
import { ElMessage } from 'element-plus/es';
import Attachment = AbcAPI.Attachment;

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    currentRows: {
        type: Object,
        default: () => {},
    },
});
const emit = defineEmits(['update:visible', 'refresh']);
const dialogVisible = computed({
    get() {
        return props.visible;
    },
    set(val) {
        emit('update:visible', val);
    },
});
const handleSubmitClick = async () => {
    let res: any = {};
    const postData = {
        attachments: currentFileDataList.value,
    };
    try {
        res = await ClinicActivityAPI.distributeRewardsUsingPUT(
            props.currentRows.clinicId, props.currentRows.editionOrderId,
            postData,
        );
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    if (res && res.code) {
        dialogVisible.value = false;
        emit('refresh');
    }
};
const currentFileList = ref([]);
const currentFileDataList = ref<Attachment[]>([]);
async function handleUploaderAfterRead(fileRead: any) {
    fileRead.status = 'uploading';
    fileRead.message = '上传中...';
    try {
        const { url } = await OSSUtil.upload({
            bucket: import.meta.env.VITE_APP_OSS_BUCKET,
            region: import.meta.env.VITE_APP_OSS_REGION,
            rootDir: 'oa/clue',
        }, fileRead.file);
        fileRead.status = 'done';
        fileRead.message = '';
        fileRead.url = url;
    } catch (e) {
        console.error('e', e);
        fileRead.status = 'failed';
        fileRead.message = '上传失败';
    }
    const { file = {} } = fileRead;
    currentFileDataList.value.push({
        fileName: file.name,
        fileSize: file.size,
        hash: file.hash,
        sort: currentFileDataList.value.length,
        url: fileRead.url,
    });
}
</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        class="complete-distribution-dialog"
        title="完成发放"
        width="30%"
    >
        <p class="complete-distribution-dialog__text">
            奖励发放对象：
            {{ `${currentRows.referrerWechatNickName || ''}/${currentRows.referrerAbcName || ''}  ${currentRows.referrerMobile || ''}` }}
        </p>
        <p class="complete-distribution-dialog__text">上传奖励发放截图（微信转账已收款截图）</p>
        <oa-uploader
            v-model="currentFileList"
            accept="image/*"
            :show-upload="true"
            upload-text="上传截图"
            :after-read="handleUploaderAfterRead"
        >
        </oa-uploader>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="handleSubmitClick">
                    确定
                </el-button>
                <el-button @click="dialogVisible = false">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>
<style lang="scss">
.complete-distribution-dialog {
    &__text {
        margin-bottom: 12px;
    }
}
</style>
