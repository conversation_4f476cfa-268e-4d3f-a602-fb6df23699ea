<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router';
import { computed, onMounted, provide, reactive, ref, watch } from 'vue';
import AdvertisementFormService
    from '@/views/content-management/advertisement/advertisement-form/advertisement-form-service';
import {
    ADVERTISEMENT_TYPE,
    RESOURCE_POSITIONS,
    PRODUCT_VERSION_LIST,
    PRODUCT_TYPE_LIST,
    FEATURE_LIST,
    CLINIC_ROLE_LIST,
    ORGAN_TYPE_LIST,
    OrganEnvList,
    MALL_NEW_OLD_CUSTOMER,
    PROD_USAGE_TYPE_LIST,
} from '@/views/content-management/constant';
import ClinicDialog from '@/views/content-management/advertisement/clinic-dialog/index.vue';
import AdvertisementResourceConfig from '@/views/content-management/advertisement/advertisement-resource-config/index.vue';
import OaAddress from '@/components/oa-address/index.vue';
import type { FormInstance, FormRules } from 'element-plus';
import { clone } from '@/utils/utils';
// @ts-ignore
import { isEqual } from '@tool/utils';
import CmsPushRegionDto = AbcAPI.CmsPushRegionDto;
import CmsPushClinicVo = AbcAPI.CmsPushClinicVo;
import { ElMessage, ElMessageBox } from 'element-plus/es';
import { useFormat } from '@/composables/date';
import useArea from '@/composables/area';
import {
    AdTypeEnum,
    ADVERTISEMENT_RESOURCE_TYPE, CmsResourceType,
} from '@/views/content-management/advertisement/advertisement-resource-config/config';
import {
    AdvertisementAttachment,
    AdvertisementAttachmentLinkOpenTypeEnum, AdvertisementAttachmentLinkTypeEnum,
    AdvertisementAttachmentTypeEnum,
} from '@/views/content-management/model';

const router = useRouter();
const route = useRoute();
const advertisementFormService = new AdvertisementFormService();
const { areaTree } = useArea();
provide('areaTree', areaTree);
const backToLastPage = () => {
    router.go(-1);
};
const pushId = route.params.id as string;
const postData = ref({
    adType: ADVERTISEMENT_TYPE[0].value, // 广告类型
    condition: {
        prodTypeList: [],
        organTypeList: [],
        prodVersionList: [],
        functionIncludeList: [],
        functionExcludeList: [],
        prodUsageTypeList: [],
        customerTypeList: [],
        clinicRoleList: [],
        organEnvList: [],
    },
    releaseVersion: '', // 发布版本 adType 为 2:迭代推送 时有效

    renderPushResList: <number[]>[], // 资源位渲染数据
    pushResList: <any[]>[], // 推送资源列表

    includeClinics: <CmsPushClinicVo[]>[],
    includeClinicIds: [], // 附加的推送门店ID列表
    excludeClinicIds: [], // 排除的门店ID列表
    excludeClinics: <CmsPushClinicVo[]>[],

    renderRegions: <any[]>[], // 选择的地区渲染数据
    regions: <CmsPushRegionDto[]>[], // 展示地区
});

const submitFormRules = reactive<FormRules>({
    adType: {
        required: true,
        trigger: 'blur',
        message: '请选择广告位',
    },
    pushResList: {
        required: true,
        trigger: 'blur',
        message: '请选择资源位',
    },
    title: {
        required: true,
        trigger: 'blur',
        message: '不能为空',
    },
    descName: {
        required: true,
        trigger: 'blur',
        message: '不能为空',
    },
    desc: {
        required: true,
        trigger: 'blur',
        message: '不能为空',
    },
    fileLink: {
        required: true,
        trigger: 'blur',
        message: '不能为空',
    },
    remark: {
        required: true,
        trigger: 'blur',
        message: '不能为空',
    },
    navigationBar: {
        required: true,
        trigger: 'blur',
        message: '不能为空',
    },
    link: {
        required: true,
        trigger: 'blur',
        message: '不能为空',
    },
    banner: {
        required: true,
        trigger: 'blur',
        message: '不能为空',
    },
    releaseVersion: {
        required: true,
        trigger: 'blur',
        message: '不能为空',
    },
    startTime: {
        required: true,
        trigger: 'blur',
        message: '不能为空',
    },

});
const submitFormRef = ref<FormInstance>();
let btnLoading = ref(false);

const pageTitle = computed(() => (pushId ? '编辑广告' : '新增广告'));
const resourcePositions = computed(() => RESOURCE_POSITIONS.filter(item => item._supportAdType.includes(postData.value.adType)));
const disabledSave = computed(() => pushId && isEqual(cachePostData.value, postData.value));

const loading = ref(false);

onMounted(() => {
    if (pushId) {
        fetchDetail();
    }
});
function changeAdTypeHandler() {
    postData.value.renderPushResList = [];
    postData.value.pushResList = [];
    postData.value.condition = {
        prodTypeList: [],
        organTypeList: [],
        prodVersionList: [],
        functionIncludeList: [],
        functionExcludeList: [],
        clinicRoleList: [],
        prodUsageTypeList: [],
        customerTypeList: [],
        organEnvList: [],
    };
}
function transRenderPostData() {
    postData.value.renderPushResList = [];
    postData.value.pushResList.forEach(item => {
        item.renderDesc = item?.desc?.split('$$') || [];
        if (!item.renderDesc.length) {
            item.renderDesc = [''];
        }
        if (item.resType) {
            postData.value.renderPushResList.push(item.resType);
        }
    });
    postData.value.condition = postData.value.condition
        || { prodTypeList: [],
            organTypeList: [],
            prodVersionList: [],
            organEnvList: [],
            functionIncludeList: [],
            functionExcludeList: [],
            prodUsageTypeList: [],
            customerTypeList: [],
            clinicRoleList: [],
        };

    postData.value.renderRegions = postData.value.regions?.map(item => [item.provinceId + '', item.cityId + '']) || [];
}

const cachePostData = ref('');
async function fetchDetail() {
    try {
        loading.value = true;
        await advertisementFormService.fetchData(pushId);
        Object.assign(postData.value, advertisementFormService.getPostData());
        transRenderPostData();
        cachePostData.value = clone(postData.value);
    } catch (e) {
        console.warn('获取广告详情失败', e);
    } finally {
        loading.value = false;
    }
}

function changePushResListHandler(val: number[]) {
    if (val.length < postData.value.pushResList.length) {
        postData.value.pushResList = postData.value.pushResList || [];
        postData.value.pushResList = postData.value.pushResList.filter(item => val.includes(item.resType));
        return;
    }
    val.forEach((item: number) => {
        const index = postData.value.pushResList.findIndex(tempItem => tempItem.resType === item);
        let attachments:AdvertisementAttachment[] = [];
        if (item === CmsResourceType.LOGIN_BANNER) {
            attachments = [{
                type: AdvertisementAttachmentTypeEnum.IMAGE,
                videoCoverUrl: '',
                videoUrl: '',
                imageUrl: '',
                link: '',
                linkOpenType: AdvertisementAttachmentLinkOpenTypeEnum.CURRENT,
                linkType: AdvertisementAttachmentLinkTypeEnum.OUT_LINK,
            }];
        }
        if (index === -1) {
            postData.value.pushResList.push({
                banner: '',
                renderDesc: [''],
                desc: '',
                descName: '',
                endTime: '',
                fileLink: '',
                fileName: '',
                link: '',
                linkType: 0,
                linkOpenType: 0,
                navigationBar: undefined,
                pushId: '',
                remark: '',
                resType: item,
                startTime: '',
                title: '',
                attachments,
            });
        }
    });
}

function removePushResItemHandler(val: number) {
    const index = postData.value.pushResList.findIndex(item => item.resType === val);
    index !== -1 && postData.value.pushResList.splice(index, 1);
}

const findResTypeTitle = (resType: number) => {
    const res = resourcePositions.value.find(item => item.resType === resType);
    return res?.label;
};

let showClinicDialog = ref<boolean>(false);
let clinicListType = ref(0);
const curClinicList = ref();
const enum ClinicType {
    DISPLAY_CLINIC = 0,
    INCLUDE_CLINIC = 1,
    EXCLUDE_CLINIC = 2
}
function openClinicListDialog(type: number) {
    showClinicDialog.value = true;
    clinicListType.value = type;
    curClinicList.value = [];
    if (clinicListType.value === ClinicType.INCLUDE_CLINIC) {
        curClinicList.value = postData.value.includeClinics;
    }
    if (clinicListType.value === ClinicType.EXCLUDE_CLINIC) {
        curClinicList.value = postData.value.excludeClinics;
    }
}

function changeRegionHandler() {
    postData.value.regions = postData.value.renderRegions.map(item => ({
        provinceId: item[0],
        cityId: item[1],
        level: 2,
    }));
}

function updateClinicList(clinicList: any) {
    if (clinicListType.value === ClinicType.INCLUDE_CLINIC) {
        postData.value.includeClinics = clinicList;
    }
    if (clinicListType.value === ClinicType.EXCLUDE_CLINIC) {
        postData.value.excludeClinics = clinicList;
    }
}

async function submitForm(formEl: any) {
    if (!formEl) return false;
    await formEl.validate(async (valid: boolean) => {
        if (valid) {
            pushId ? await updateAdvertisementHandler() : await createAdvertisementHandler();
        }
    });
}

function transformPostData(data:any) {
    const curPostData = clone(data.value);
    curPostData.pushResList?.map((item: any) => {
        delete item.renderDesc;
        if (!pushId) {
            delete item.pushId;
        }
        item.startTime = item.startTime || useFormat(new Date(), 'YYYY-MM-DD HH:mm:ss');
        return item;
    });
    curPostData.includeClinicIds = curPostData.includeClinics?.map((item: CmsPushClinicVo) => item.clinicId);
    curPostData.excludeClinicIds = curPostData.excludeClinics?.map((item: CmsPushClinicVo) => item.clinicId);
    delete curPostData.renderRegions;
    delete curPostData.includeClinics;
    delete curPostData.excludeClinics;
    delete curPostData.renderPushResList;
    return curPostData;
}
/**
 * @desc 新增广告
 * <AUTHOR>
 * @date 2022-07-13 20:04:18
 */
async function createAdvertisementHandler() {
    try {
        btnLoading.value = true;
        const data = transformPostData(postData);
        await advertisementFormService.create(data);
        ElMessage({
            message: '保存成功',
            type: 'success',
        });
        router.push({
            name: '@contentManagement/advertisement/record',
        });
    } catch (e: any) {
        btnLoading.value = false;
        ElMessageBox({
            type: 'error',
            message: e.message,
        });
    }
}

async function updateAdvertisementHandler() {
    try {
        btnLoading.value = true;
        const data = transformPostData(postData);
        await advertisementFormService.update(pushId, data);
        ElMessage({
            message: '保存成功',
            type: 'success',
        });
        router.push({
            name: '@contentManagement/advertisement/record',
        });
    } catch (e: any) {
        btnLoading.value = false;
        ElMessageBox({
            type: 'error',
            message: e.message,
        });
    }
}

const showOrganEnvList = computed(() => [AdTypeEnum.ITERATION].includes(postData.value.adType));
</script>

<template>
    <div class="oa-advertisement-add-wrapper">
        <div class="handler-bar">
            <el-button @click="backToLastPage">返回</el-button>
            <el-divider direction="vertical"></el-divider>
            <h2>{{ pageTitle }}</h2>
        </div>

        <el-form
            ref="submitFormRef"
            :model="postData"
            :rules="submitFormRules"
            class="push-form-wrapper"
            label-position="left"
        >
            <section class="settings-group">
                <h3>展示设置</h3>
                <el-divider></el-divider>

                <el-form-item label="广告分类" prop="adType">
                    <el-radio-group v-model="postData.adType" @change="changeAdTypeHandler">
                        <el-radio v-for="item in ADVERTISEMENT_TYPE" :key="item.label" :label="item.value">{{ item.label }}</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="资源位" prop="pushResList" trigger="blur">
                    <el-select
                        v-model="postData.renderPushResList"
                        multiple
                        placeholder="请选择"
                        @change="changePushResListHandler"
                        @remove-tag="removePushResItemHandler"
                    >
                        <el-option
                            v-for="item in resourcePositions"
                            :key="item.resType"
                            :label="item.label"
                            :value="item.resType"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <template v-if="[AdTypeEnum.AUTO, AdTypeEnum.CONDITION].includes(postData.adType)">
                    <el-form-item label="展示地区">
                        <oa-address v-model="postData.renderRegions" @change="changeRegionHandler">
                        </oa-address>
                    </el-form-item>

                    <el-form-item label="产品类型">
                        <el-checkbox-group v-model="postData.condition.prodTypeList">
                            <el-checkbox v-for="item in PRODUCT_TYPE_LIST" :label="item.value">{{ item.label }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>

                    <el-form-item label="用药类型">
                        <el-checkbox-group v-model="postData.condition.organTypeList">
                            <el-checkbox v-for="item in ORGAN_TYPE_LIST" :label="item.value">{{ item.label }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>

                    <el-form-item label="产品版本">
                        <el-checkbox-group v-model="postData.condition.prodVersionList">
                            <el-checkbox v-for="item in PRODUCT_VERSION_LIST" :label="item.value">{{ item.label }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>

                    <el-form-item label="产品使用度">
                        <el-checkbox-group v-model="postData.condition.prodUsageTypeList">
                            <el-checkbox v-for="item in PROD_USAGE_TYPE_LIST" :label="item.value">{{ item.label }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="商城药品新老客">
                        <el-checkbox-group v-model="postData.condition.customerTypeList">
                            <el-checkbox v-for="item in MALL_NEW_OLD_CUSTOMER" :label="item.value">{{ item.label }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>

                    <el-form-item label="功能包含">
                        <el-checkbox-group v-model="postData.condition.functionIncludeList">
                            <el-checkbox v-for="item in FEATURE_LIST" :label="item.value">{{ item.label }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>

                    <el-form-item label="功能排除">
                        <el-checkbox-group v-model="postData.condition.functionExcludeList">
                            <el-checkbox v-for="item in FEATURE_LIST" :label="item.value">{{ item.label }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>

                    <el-form-item label="门店角色">
                        <el-checkbox-group v-model="postData.condition.clinicRoleList">
                            <el-checkbox v-for="item in CLINIC_ROLE_LIST" :label="item.value">{{ item.label }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </template>
                <el-form-item v-if="showOrganEnvList" label="版本标签">
                    <el-checkbox-group v-model="postData.condition.organEnvList">
                        <el-checkbox v-for="item in OrganEnvList" :label="item.value">{{ item.label }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>

                <el-form-item v-if="postData.adType === ADVERTISEMENT_TYPE[2].value" label="发布版本">
                    <el-input v-model="postData.releaseVersion" link></el-input>
                </el-form-item>
                <el-form-item label="展示门店">
                    <el-button text type="primary" @click="openClinicListDialog(ClinicType.DISPLAY_CLINIC)">详情</el-button>
                </el-form-item>
                <el-form-item label="新增门店">
                    <el-button text type="primary" @click="openClinicListDialog(ClinicType.INCLUDE_CLINIC)">详情</el-button>
                </el-form-item>
                <el-form-item label="排除门店">
                    <el-button text type="primary" @click="openClinicListDialog(ClinicType.EXCLUDE_CLINIC)">详情</el-button>
                </el-form-item>
            </section>

            <section class="settings-group" style="margin-top: 48px;">
                <h3>展示内容</h3>
                <el-divider></el-divider>
                <div class="group-content">
                    <advertisement-resource-config
                        v-for="(item, index) in postData.pushResList"
                        :key="item.resType"
                        v-model:push-res-item="postData.pushResList[index]"
                        :res-type="item.resType"
                        :rules="submitFormRules"
                        :push-res-list="postData.pushResList"
                        :config-index="index"
                    >
                        <template #title>
                            <h4 style="margin-bottom: 12px;">{{ findResTypeTitle(item.resType) }}</h4>
                        </template>
                    </advertisement-resource-config>
                </div>
            </section>
        </el-form>
        <el-divider></el-divider>
        <el-button
            :loading="btnLoading"
            type="primary"
            style="margin-bottom: 24px;"
            size="large"
            @click="submitForm(submitFormRef)"
        >
            保存
        </el-button>
    </div>
    <clinic-dialog
        v-if="showClinicDialog"
        v-model:visible="showClinicDialog"
        :condition="postData.condition"
        :ad-type="postData.adType"
        :clinic-type="clinicListType"
        :clinics="curClinicList"
        :regions="postData.renderRegions"
        :push-id="pushId"
        @close="updateClinicList"
    ></clinic-dialog>
</template>

<style lang="scss" scoped>
.oa-advertisement-add-wrapper {
    .handler-bar {
        display: flex;
        align-items: center;
        padding-bottom: 24px;
        border-bottom: 1px solid var(--oa-gray-5);
        margin-bottom: 24px;
    }

    .push-form-wrapper {
        ::v-deep .el-form-item__content {
            margin-left: 6px;
        }
    }

    .settings-group {
        h3 {
            margin-bottom: 12px;
        }

        ::v-deep .el-form-item {
            margin-bottom: 24px;

            .el-form-item__label {
                width: 110px;
                color: #7a8794;
            }

            .el-radio__label,
            .el-checkbox__label {
                color: #000;
                font-weight: normal;
            }

            .el-checkbox,
            .el-radio {
                margin-right: 0;
                width: 100px;
            }

            .el-input {
                width: 300px;
            }

            .label-text {
                font-size: 14px;
                color: var(--el-text-color-regular);
                font-weight: bold;
                margin-left: 4px;
            }
        }

        ::v-deep .el-divider {
            margin: 12px 0;
        }

        .group-content {
            .oa-advertisement-resource-config-wrapper {
                margin-bottom: 48px;
            }
        }
    }
}
</style>
