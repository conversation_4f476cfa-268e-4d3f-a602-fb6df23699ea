// eslint-disable-next-line max-classes-per-file
const MapKey = 'LH7BZ-KKRWO-MSTWX-SQS2W-XR5H7-SUFUY';
const TMap = (window as any).TMap;

export const transferAddressData = (depth = 1, address: any = {}) => {
    const obj: any = address['100000'];

    function loop(obj: any, cache = new Set(), currentDepth = 0, parent: any) {
        if (cache.has(obj)) {
            return [];
        } 
        cache.add(obj);
        
        const result = [];
        currentDepth++;
        for (const key in obj) {
            const _item: any = {};
            _item.title = obj[key];
            _item.label = obj[key];
            _item.value = `${key}/${obj[key]}`;
            _item.depth = currentDepth;
            _item.id = key;
            if (parent) {
                _item.key = `${parent.key || ''}${parent.key ? '-' : ''}${
                    _item.value
                }`;
            }
            result.push(_item);
            if (address[key] && currentDepth < depth) {
                _item.children = loop(address[key], cache, currentDepth, _item);
            } else {
                _item.isLeaf = true;
            }
        }
        return result;
    }

    return Object.freeze(loop(obj, new Set(), 0, { path: '10000' }));
};
export const getGeolocation = () => {
    const qq = (window as any).qq;
    let geolocation = new qq.maps.Geolocation(MapKey, 'myapp');
    return new Promise((resolve, reject) => {
        geolocation.getLocation((position: any) => {
            resolve(position);
        }, (err: any) => {
            reject(err);
        });
    });
};

export const createMapStyles = () => ({
    myStyle: new TMap.MarkerStyle({
        width: 20, // 点标记样式宽度（像素）
        height: 30, // 点标记样式高度（像素）
        anchor: { x: 10, y: 15 }, // 圆形点以圆心位置为焦点
    }),
    countStyle: new TMap.MarkerStyle({
        width: 0, // 点标记样式宽度（像素）
        height: 0, // 点标记样式高度（像素）
        anchor: { x: 0, y: 0 }, // 圆形点以圆心位置为焦点
    }),
});

export const getGeometries = (data?: any[]) => {
    if (!data || !data.length) {
        return [];
    }
    const result = data.map((item: any) => ({
        position: item.lng && item.lat ? new TMap.LatLng(item.lat, item.lng) : '',
        content: item.content || `${(item.distance).toFixed(2) || '?'}KM  ${item.organName.length > 10
            ? item.organName?.substring(0, 10) + '...' : item.organName}`,
        province: item.provinceName,
        city: item.cityName,
        distract: item.distractName,
        clinicId: item.clinicId,
    }));
    return result;
};

export class ClusterBubble extends TMap.DOMOverlay {
    options: any

    constructor(options: any) {
        super(options);
        this.options = options;
    }

    onInit(options: any) {
        this.content = options.content;
        this.position = options.position;
    }

    // 销毁时需要删除监听器
    onDestroy() {
        this.dom.removeEventListener('click', this.onClick);
        this.removeAllListeners();
    }

    onClick() {
        this.emit('click');
    }

    onTouchstart() {
        this.emit('touchstart');
    }

    onTouchend() {
        this.emit('touchend');
    }

    // 创建气泡DOM元素
    createDOM() {
        let dom = document.createElement('div');
        dom.classList.add('clusterBubble');
        dom.classList.add('flex-center');
        dom.innerText = this.content;
        dom.style.cssText = [
            'padding: 14px;',
            'aspect-ratio: 1/1',
        ].join(' ');

        // 监听点击事件，实现zoomOnClick
        this.onClick = this.onClick.bind(this);
        this.onTouchstart = this.onTouchstart.bind(this);
        this.onTouchend = this.onTouchend.bind(this);
        dom.addEventListener('click', this.onClick);
        dom.addEventListener('touchstart', this.onTouchstart);
        dom.addEventListener('touchend', this.onTouchend);
        return dom;
    }

    updateDOM() {
        if (!this.map) {
            return;
        }
        // 经纬度坐标转容器像素坐标
        let pixel = this.map.projectToContainer(this.position);

        // 使文本框中心点对齐经纬度坐标点
        this.dom.style.left = pixel.x - 20 + 'px';
        this.dom.style.top = pixel.y - 20 + 'px';
    }
}

export class TextBubble extends ClusterBubble {
    // 创建气泡DOM元素
    createDOM() {
        let dom = document.createElement('div');
        dom.classList.add('textBubble');
        dom.innerText = this.content;
        dom.style.cssText = [
            'padding: 4px;',
        ].join(' ');

        // 监听点击事件，实现zoomOnClick
        this.onClick = this.onClick.bind(this);
        this.onTouchstart = this.onTouchstart.bind(this);
        this.onTouchend = this.onTouchend.bind(this);
        dom.addEventListener('click', this.onClick);
        dom.addEventListener('touchstart', this.onTouchstart);
        dom.addEventListener('touchend', this.onTouchend);
        return dom;
    }
}/**
 * 坐标集合的最西南角
 * @param {*} list
 * @param {*} isCluster 是否是聚合点
 *  list 是接口获取的点 的数组
 */
export const getSW = (list: {longitude: number, latitude: number}[], isCluster = false) => {
    let south = null;
    let west = null;
    for (let item of list) {
        if ((west && item.longitude < west) || !west) {
            west = item.longitude - 0.01 - (isCluster ? 0.2 : 0);
        }
        if ((south && item.latitude < south) || !south) {
            south = item.latitude - 0.01 - (isCluster ? 0.2 : 0);
        }
    }
    return new TMap.LatLng(south, west);
};

/**
 *  最东北角
 * @param {*} list
 * @param {*} isCluster 是否是聚合点
 */
export const getNE = (list: {longitude: number, latitude: number}[], isCluster = false) => {
    let north = null;
    let east = null;
    for (let item of list) {
        if ((east && item.longitude > east) || !east) {
            east = item.longitude + 0.01 + (isCluster ? 0.2 : 0);
        }
        if ((north && item.latitude > north) || !north) {
            north = item.latitude + 0.01 + (isCluster ? 0.2 : 0);
        }
    }

    return new TMap.LatLng(north, east);
};

/**
 *  最东北角
 * @param {*} list
 * @param {*} isCluster 是否是聚合点
 */
export const getCenter = (list: {longitude: number, latitude: number}[]) => {
    const center = {
        longitude: 0,
        latitude: 0,
    };
    const maxLng = Math.max(...list.map(item => item.longitude));
    const minLng = Math.min(...list.map(item => item.longitude));
    const maxLat = Math.max(...list.map(item => item.latitude));
    const minLat = Math.min(...list.map(item => item.latitude));
    center.longitude = (maxLng + minLng) / 2;
    center.latitude = (maxLat + minLat) / 2;

    return new TMap.LatLng(center.latitude, center.longitude);
};

export const middle = (args: any[]) => {
    if (!args || !args.length) {
        return null;
    }
    args = args.sort((a, b) => a - b); // 排序
    if (args.length % 2 === 0) {
        // 判断数字个数是奇数还是偶数
        return (+args[args.length / 2] + +args[args.length / 2 - 1]) / 2; // 偶数个取中间两个数的平均数
    } 
    return args[parseInt(String(args.length / 2))]; // 奇数个取最中间那个数
};