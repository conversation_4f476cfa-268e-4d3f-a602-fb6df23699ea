<script setup lang="ts">
import { onMounted, reactive, getCurrentInstance, ref, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { LineCharts } from '@/composables/line-charts';
import { CrmClientApi } from '@/api/crm-client-api';
import CrmDetailVo = AbcAPI.CrmDetailVo;
import dayjs from 'dayjs';
import { Toast } from 'vant';

const route = useRoute();
const { appContext } = getCurrentInstance() as any;
const selectOptions = reactive([
    {
        key: 'status',
        options: [
            {
                value: '',
                text: '状态',
            },
            {
                value: '0',
                text: '不限',
            },
            {
                value: '1',
                text: '非活跃',
            },
            {
                value: '2',
                text: '新门店',
            },
            {
                value: '3',
                text: '活跃',
            },
            {
                value: '4',
                text: '即将过期',
            },
            {
                value: '5',
                text: '等待续费',
            },
        ],
    },
    {
        key: 'version',
        options: [
            {
                value: '',
                text: '版本',
            },
            {
                value: '0',
                text: '不限',
            },
            {
                value: '10',
                text: '基础版',
            },
            {
                value: '20',
                text: '专业版',
            },
            {
                value: '30',
                text: '旗舰版',
            },
            {
                value: '40',
                text: '大客户版',
            },

        ],
    },
    {
        key: 'type',
        options: [
            {
                value: '',
                text: '类型',
            },
            {
                value: '0',
                text: '普通诊所',
            },
            {
                value: '1',
                text: '口腔诊所',
            },
            {
                value: '2',
                text: '眼科诊所',
            },
        ],
    },
    {
        key: 'wechat',
        options: [
            {
                value: '',
                text: '企微',
            },
            {
                value: '0',
                text: '不限',
            },
            {
                value: '1',
                text: '已添加',
            },
            {
                value: '2',
                text: '未添加',
            },
        ],
    },
]);
let contactList = reactive<any>([]);
interface ClientInfo {
    [key: string]: any;
}
const clientInfo = reactive<ClientInfo>({
    organName: '',
    organId: '',
    organCity: '',
    organSeller: '',
    organSellerId: '',
    clientType: '',
    clientNeeds: '',
    version: '',
});
const clientEchartsData = reactive({
    clientChargesPeriod: '7',
    showSheet: false,
    actions: [
        { name: '收费次数', value: 'clientChargesPeriod' },
    ],
    chargeActionData: {
        amountCountList: [],
        createdDateList: [],
    },
    title: '收费次数',

});
const clientEcharts = ref(null);
const myChart = ref<any>();
const clientId = ref<any>('');
const loading = ref(false);
onMounted(async () => {
    clientId.value = route.params.organId || '';
    await getClientInfo();
});
const getClientInfo = async () => {
    loading.value = true;
    let res = <CrmDetailVo>{};
    try {
        res = await CrmClientApi.getApiLowCodeCrmDetailById(clientId.value || '');
    } catch (error: any) {
        loading.value = false;
        Toast({
            type: 'fail',
            message: '获取客户信息失败',
        });
        return false;
    }
    if (res) {
        Object.assign(clientInfo, { ...clientInfo, ...res });
        Object.assign(contactList, res.employeeList || []);
        await getChargeActionList();
        loading.value = false;
    }
};

/**
 * 获取主要联系人身份
 * @param contact
 */
const getTagName = (contact: any) => {
    if (contact && contact.tags && contact.tags.length > 0) {
        const tag = contact.tags.find((item: any) => item.tagName === '关键决策人' || item.tag_name === '店长/馆长');
        return tag ? tag.tagName : '';
    }
    return '';
};

/**
 * 设置图表数据配置
 *
 */
const setChartsOptions = async () => {
    if (myChart.value && myChart.value.dispose) {
        // 移去上次渲染的结果，确保本次不受影响
        myChart.value.dispose();
    }
    await nextTick(() => {
        myChart.value = appContext.config.globalProperties.$echarts.init(clientEcharts.value);
    });
    const option = new LineCharts();
    option.setTitle('收费次数');
    option.setXAxis(clientEchartsData.chargeActionData.createdDateList);
    option.setSeries(clientEchartsData.chargeActionData.amountCountList, '收费次数');
    myChart.value.setOption(option.getOption());
};

/**
 * 获取下拉框标题
 * @param key
 */
const getOptionText = (key:string, value: any) => {
    const option = selectOptions.find(item => item.key === key);
    return option?.options?.find((item: any) => item.value === value)?.text;
};
/**
 * 切换图表类型
 * @param item
 */
const onSelect = (item: any) => {
    clientEchartsData.showSheet = false;
    clientEchartsData.title = item.name;
};

/**
 * 格式化时间
 * @param date
 * @param fmt
 */
const formatDate = (date: any, fmt = 'YYYY-MM-DD') => (date ? dayjs(date).format(fmt) : '');

/**
 * 切换时间轴期限
 * @param item
 */
const changeChargesPeriod = async (item: any) => {
    clientEchartsData.clientChargesPeriod = item;
    await getChargeActionList();
};

/**
 * 查询图表数据
 */
const getChargeActionList = async () => {
    let res = <any>{};
    try {
        res = await CrmClientApi.getApiLowCodeCrmChargeAction(clientEchartsData.clientChargesPeriod, clientId.value || '');
    } catch (error: any) {
        Toast({
            type: 'fail',
            message: '获取客户收费记录失败',
        });
        return false;
    }
    if (res) {
        clientEchartsData.chargeActionData = res;
        await setChartsOptions();
    }
};

/**
 * 复制手机号
 * @param mobile
 */
const copyMobile = (mobile: string) => {
    const input = document.createElement('input');
    input.value = mobile;
    document.body.appendChild(input);
    input.select();
    document.execCommand('Copy');
    document.body.removeChild(input);
    Toast({
        type: 'success',
        message: '复制成功',
    });
};
</script>

<template>
    <div class="client-detail-wrapper">
        <van-loading
            v-if="loading"
            class="client-detail-loading"
            type="spinner"
            color="#1989fa"
        ></van-loading>
        <van-cell class="client-detail-title-info">
            <template #value>
                <p class="client-detail-title-info__title">{{ clientInfo.organName || '诊所名称' }}</p>
                <van-row>
                    <van-col span="12">
                        <span class="client-detail-title-info__city client-detail-title-info__item">所在城市：
                            {{ (clientInfo.addressProvinceName || '') + `${clientInfo.addressCityName ? `-${clientInfo.addressCityName}` : ''}` }}
                        </span>
                    </van-col>
                    <van-col span="12">
                        <span class="client-detail-title-info__seller client-detail-title-info__item">客户经理：{{ clientInfo.sellerName }}</span>
                    </van-col>
                    <!-- TODO：产品说先不要-->
                    <!-- <van-col span="12">-->
                    <!--     <span class="client-detail-title-info__city client-detail-title-info__item">客户类型：{{ clientInfo.clientType }}</span>-->
                    <!-- </van-col>-->
                    <!-- <van-col span="12">-->
                    <!--     <span class="client-detail-title-info__city client-detail-title-info__item">客户需求：-->
                    <!--         {{ getOptionText('type', clientInfo.hisType?.toString()) }}-->
                    <!--     </span>-->
                    <!-- </van-col>-->
                </van-row>
            </template>
        </van-cell>
        <van-cell class="client-order-info">
            <template #value>
                <van-row>
                    <van-col span="10">
                        <div class="client-order-info__item">
                            {{ getOptionText( 'version', clientInfo.editionId) || '暂未开通' }}
                            <p class="client-order-info__label">当前版本</p>
                        </div>
                    </van-col>
                    <van-col span="2" class="divider-line-wrapper">
                        <div class="divider-line"></div>
                    </van-col>
                    <van-col span="12">
                        <div class="client-order-info__item">
                            {{ `¥${clientInfo.paidFee}` || '暂无收费' }}
                            <p class="client-order-info__label">上次收费</p>
                        </div>
                    </van-col>
                    <van-col span="10">
                        <div class="client-order-info__item">
                            {{ formatDate(clientInfo.beginDate) || '--' }}
                            <p class="client-order-info__label">开始时间</p>
                        </div>
                    </van-col>
                    <van-col span="2" class="divider-line-wrapper">
                        <div class="divider-line"></div>
                    </van-col>
                    <van-col span="12">
                        <div class="client-order-info__item">
                            {{ formatDate(clientInfo.endDate)|| '--' }}
                            <p class="client-order-info__label">结束时间</p>
                        </div>
                    </van-col>
                </van-row>
            </template>
        </van-cell>
        <van-cell class="client-main-contact">
            <template #title>
                <span>主要联系人</span>
            </template>
            <template v-if="contactList && contactList.length" #value>
                <van-cell v-for="contact in contactList">
                    <van-row>
                        <van-col span="6">
                            <span class="client-main-contact__name">{{ contact.name }}</span>
                        </van-col>
                        <van-col span="10">
                            <span class="client-main-contact__identity">{{ getTagName(contact) || contact.identity }}</span>
                        </van-col>
                        <van-col span="8" class="client-main-contact__mobile">
                            <span>{{ contact.mobile }}</span>
                            <el-icon @click="copyMobile(contact.mobile)"><DocumentCopy /></el-icon>
                        </van-col>
                    </van-row>
                </van-cell>
            </template>
            <template v-else #value><van-empty description="暂无主要联系人" /></template>
        </van-cell>
        <van-cell class="client-echarts-wrapper">
            <template #title>
                <div class="client-echarts-select" @click="clientEchartsData.showSheet = true">
                    <span>{{ clientEchartsData.title }}</span><van-icon name="arrow-down" />
                </div>
            </template>
            <template #value>
                <van-tag
                    plain
                    class="client-charges-period"
                    :class="{active: clientEchartsData.clientChargesPeriod === '7'} "
                    @click="changeChargesPeriod('7')"
                >
                    近7天
                </van-tag>
                <van-tag
                    plain
                    class="client-charges-period"
                    :class="{active: clientEchartsData.clientChargesPeriod === '15'}"
                    @click="changeChargesPeriod('15')"
                >
                    近15天
                </van-tag>
                <van-tag
                    plain
                    class="client-charges-period"
                    :class="{active: clientEchartsData.clientChargesPeriod === '30'}"
                    @click="changeChargesPeriod('30')"
                >
                    近30天
                </van-tag>
            </template>
        </van-cell>
        <div id="clientEcharts" ref="clientEcharts" class="client-echarts"></div>
        <van-action-sheet v-model:show="clientEchartsData.showSheet" :actions="clientEchartsData.actions" @select="onSelect" />
    </div>
</template>
<style lang="scss">

.client-detail-wrapper {
    width: 100%;
    position: absolute;
    height: 100vh;
    overflow: auto;

    .client-detail-loading {
        position: absolute;
        top: 0;
        z-index: 1;
        width: 100vw;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(0, 0, 0, .2);
    }

    .van-cell + .van-cell {
        margin-top: 8px;
    }

    .client-detail-title-info {
        padding: 14px;

        .client-detail-title-info__title {
            font-size: 16px;
            font-weight: bold;
        }

        .client-detail-title-info__item {
            font-size: 12px;
            color: #848c97;
        }
    }

    .client-order-info {
        padding: 14px;

        .client-order-info__item {
            font-size: 13px;
            color: #383838;
        }

        .client-order-info__label {
            font-size: 12px;
            color: #848c97;
            font-weight: bold;
        }

        .divider-line-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;

            .divider-line {
                width: 1px;
                height: 80%;
                background-color: #ececec;
            }
        }
    }

    .client-main-contact {
        display: block;

        .van-cell__title {
            font-size: 13px;
            font-weight: bold;
            color: #383838;
        }

        .client-main-contact__name {
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .client-main-contact__identity {
            font-size: 12px;
            color: #848c97;
        }

        .client-main-contact__mobile {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

    .client-echarts-wrapper {
        .client-charges-period {
            font-size: 12px;
            color: #848c97;
            margin-right: 8px;
            padding: 4px;

            &.active {
                color: #09b148;
            }
        }
    }

    .client-echarts {
        width: 100%;
        height: 240px;
        background: #fff;
        padding-left: 8px;
    }
}
</style>
