declare namespace  AbcAPI {
    
    type GoodsPurchasePlanView = {    
        
        chainId:string    
        
        completeDate:string    
        
        count:number    
        
        createdDate:string    
        
        createdUser?:EmployeeView    
        
        id:string    
        
        isComplete:number    
        
        kindCount:number    
        
        lastModifiedDate:string    
        
        lastModifiedUserId:string    
        
        list?:Array<GoodsPurchasePlanItemView>    
        
        name:string    
    }
}
