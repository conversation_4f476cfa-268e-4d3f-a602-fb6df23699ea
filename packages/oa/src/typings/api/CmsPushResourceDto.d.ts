declare namespace  AbcAPI {
    
    /**
     * 推送广告信息
     */
    type CmsPushResourceDto = {    
        //图标或banner
        banner?:string    
        //描述/正文/摘要
        desc?:string    
        //描述名名称/正文标题
        descName?:string    
        //展示结束时间
        endDate?:string    
        //展示结束时间
        endTime?:string    
        //扩展数据
        extendData?:JsonNode    
        //文件链接
        fileLink?:string    
        //文件名称
        fileName?:string    
        //链接
        link?:string    
        //链接名称
        linkName?:string    
        //链接打开类型 0:新标签页打开 1:当前页面打开
        linkOpenType?:number    
        //链接类型 0:内部链接 1:外部链接
        linkType?:number    
        //导航栏 0:工作台 1:执行站 2:挂号预约 3:门诊 4:儿保 5:收费 6:药房 7:检验 8:采购 9:库存 10:患者 11:统计 12:管理 13:商城 14:社保
        navigationBar?:number    
        //推送ID，更新时必传
        pushId?:string    
        //备注
        remark?:string    
        //资源位类型 1:工作台顶部 2:消息中心(内容中心) 3:工作台弹窗 4:全量更新通知 5:登录页 Banner 6:工作台右侧 7:紧急通知 8:导航栏气泡 9:APP 推送 10:工作台Banner
        resType?:number    
        //展示开始时间
        startDate?:string    
        //展示开始时间
        startTime?:string    
        //标题
        title?:string    
    }
}
