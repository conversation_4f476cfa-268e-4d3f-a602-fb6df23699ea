declare namespace  AbcAPI {
    
    type QueryCmsPushDetailRsp = {    
        //广告类型 0:全量展示 1:条件展示 2:迭代推送
        adType:number    
        //推送条件
        condition?:CmsPushConditionVo    
        //排除门店
        excludeClinics?:Array<CmsPushClinicVo>    
        //附加的推送门店
        includeClinics?:Array<CmsPushClinicVo>    
        //推送资源列表
        pushResList?:Array<CmsPushResourceVo>    
        //展示地区
        regions?:Array<CmsPushRegionVo>    
        //发布版本 adType 为 2:迭代推送 时有效
        releaseVersion:string    
    }
}
