declare namespace  AbcAPI {
    
    type CalGoodsPriceItem = {    
        //要算的大包价格 可选
        calPackagePrice?:GoodsPriceItem    
        //要算的小包价格,【可选】为了后面的可扩展性，piecePrice单独独立一个字段出来
        calPiecePrice?:GoodsPriceItem    
        //利润率计算 可选
        calProfitRat?:CalProfitRat    
        //计算结果 不抛异常，返回结算结果是否OK 0 success
        calResult:number    
        //计算类型  1 按piecePrice /packagePrice 算 ；2 按packagePrice/pieceNum算,3算利润率。(对于操作类型 2/hack逻辑，前端不好改，在参数检查之前进行一个hack操作 对于按进价调价，如果进价不为空，把大包价格的before 强设置成 算利润率里面的 成本价。）
        calType:number    
        //回射KeyId，返回计算的售价，目前为GoodsId
        goodsId:string    
        //计算类型 2 时如果不为null ，需要算calPiecePrice，前端要判断在拆零的时候才算这个
        pieceNum:number    
    }
}
