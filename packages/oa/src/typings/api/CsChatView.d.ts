declare namespace  AbcAPI {
    
    type CsChatView = {    
        //分配规则，0:平均分配，1:上次接待优先，2:他人转接
        assignRule:number    
        //绑定机构信息列表
        bindOrganInfoList?:Array<ChatOrganView>    
        //id
        id:number    
        //接待人员是否在会话中，当有新的分配（chat-assign事件）时该标记才会返回（其他时候为null），0:不在会话中，1:在会话中
        isShiftStaffInChat:number    
        //最近一条消息
        latestMsg?:CsChatMsgItemView    
        //最大版本id
        maxEditionId:string    
        //最大版本名
        maxEditionName:string    
        //成员列表
        memberList?:Array<CsChatMemberView>    
        //会话名
        name:string    
        //直接加群二维码，临时二维码，只需要扫一次码，需要结合有效期使用，可能为空
        qwChatDirectQrCode:string    
        //直接加群二维码过期时间
        qwChatDirectQrCodeExpiredTime:string    
        //企业微信会话id
        qwChatId:string    
        //加群二维码，永久二维码，需要扫两次码
        qwChatQrCode:string    
        //接待记录id
        receptionId:number    
        //备注，null 无备注；非null 有备注，可能为空字符串
        remark:string    
        //当前待回复的超时分钟数
        replayTimeoutMinutes:number    
        //编号
        sequenceNumber:string    
        //排序加权，目前是按照版本大小计算出来的，1 基础版，2 专业版，3 旗舰版，4 大客户版，5 医院版本
        sortWeight:number    
        //转接时添加的备注
        transferRemark:string    
        //转接人id
        transferShiftStaffId:string    
        //转接人名
        transferShiftStaffName:string    
        //转接状态，null/0:无需处理；10:待处理；20:已处理
        transferStatus:number    
        //未读消息数
        unreadMsgCount:number    
    }
}
