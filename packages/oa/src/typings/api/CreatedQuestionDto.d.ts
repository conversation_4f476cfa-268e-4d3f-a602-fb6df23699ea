declare namespace  AbcAPI {

    type CreatedQuestionDto = {
        //id
        id?: any
        //分类id
        categoryId?:number
        //排序
        sort?:number
        //标题
        title?:string
        //问题类型（0：普通，1：新手教学）
        type?:number
        //内容
        content:string
        //视频json
        videos?:Array<any>
        //角色id列表
        roleIds?:Array<any>
        //绑定问题id列表
        questions?:Array<any>
        //功能id列表
        funcIds?:Array<any>
        //是否隐藏
        isHide?:number
        //是否推荐
        isRecommend?:number
        //查看数
        viewsCount?:number
    }
}
