declare namespace  AbcAPI {
    
    type DiagnosisAndTreatGoodsView = {    
        //是否子店定价
        allowSubClinicSetPrice:boolean    
        //业务自定义扩展，业务吸入业务理解(目前检查检验会写入：采样类型)
        bizExtensions?:JsonNode    
        //goods关联的业务相关Id 检查  检查设备型号id，检验 检验设备的id
        bizRelevantId:string    
        //药品总店的禁用状态
        chainDisable:number    
        //如果是套餐，套餐的子项
        children?:Array<ComposeGoodsListItemSimpleView>    
        //组合类型，套餐和检查检验存在
        combineType:number    
        
        customTypeId:string    
        
        customTypeName:string    
        //bizRelevantId  对应的检验设备型号
        deviceInfo?:GoodsBindDeviceInfo    
        //药品禁用状态
        disable:number    
        //后台已经组装好的可直接显示的名字
        displayName:string    
        //后台已经组装好的可直接显示的规格
        displaySpec:string    
        //检查检验特有 代码,比如红细胞在业界的通用名字
        enName:string    
        //商品goodsId
        id:string    
        //是否对外销售
        isSell:number    
        //义齿加工 厂家的名字
        manufacturer:string    
        //cadn
        medicineCadn:string    
        //商品商品名
        name:string    
        //是否需要指定，值对治疗理疗有效
        needExecutive:number    
        //价格三元组
        packageCostPrice:number    
        //价格三元组
        packagePrice:number    
        //单位
        packageUnit:string    
        //非库存商品，这个基本上都为1
        pieceNum:number    
        //价格三元组
        piecePrice:number    
        
        remark:string    
        //国家医保对码视图
        shebaoNationalView?:GoodsShebaoView    
        //医保支付方式 0 正常支付  1 自费支付 2 禁止医保支付
        shebaoPayMode:number    
        //医保支付方式 0 正常支付  1 自费支付 2 禁止医保支付
        shebaoPayModeName:string    
        //商品ShortId
        shortId:string    
        //药品子类型
        subType:number    
        //义齿加工 厂家的ID，也就是supplierId
        supplierId:string    
        //药品类型
        type:number    
        //typeId ->(type,subType,cMSpec三元组的id)
        typeId:number    
    }
}
