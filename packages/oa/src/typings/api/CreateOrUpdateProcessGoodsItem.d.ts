declare namespace  AbcAPI {
    
    type CreateOrUpdateProcessGoodsItem = {    
        //分类ID
        customTypeId?:number    
        //分类名字，在导入的时候用用
        customTypeName?:string    
        //启用停用状态
        disable?:number    
        //goodsId 加工项目Id
        id?:string    
        //goodsId 加工项目名字
        name?:string    
        //0 新增 1 修改 2 删除 一定要前端告诉明确删才删，否则不删 因为可能有于网络原因 前端没有拉到原来的项目列表，这个时候新加一条 不要把老的误删了
        opType?:number    
        //成本加，留着避免产品上使用
        packageCostPrice?:number    
        //售价
        packagePrice?:number    
        //单位
        packageUnit?:string    
        //义齿加工备注
        remark?:string    
    }
}
