declare namespace  AbcAPI {
    
    /**
     * 诊所发票列表企微视图
     */
    type ClinicInvoiceApplyViewForQw = {    
        //诊所ID
        clinicId:string    
        //诊所名称
        clinicName:string    
        //客户联系信息 地址 + 客户名称 + 客户手机号
        contactInfo:string    
        //开票申请创建时间
        invoiceApplyCreated:string    
        //开票申请ID
        invoiceApplyId:string    
        //开票项目类型
        invoiceOrderCategory:number    
        //开票订单ID
        invoiceOrderId:string    
        //开票订单名称
        invoiceOrderName:string    
        //订单时间
        invoiceOrderTime:string    
        //开票状态 0:待开票 10:开票中 20:已完成 30:开票失败
        invoiceStatus:number    
        //显示的开票抬头
        invoiceTitle:string    
        //开票总金额
        invoiceTotalPrice:number    
        //显示的开票类型 1：普通发票，2：增值税专用发票
        invoiceType:number    
        //发票地址
        invoiceUrl:string    
        //接收账号id
        receiveAccountId:string    
        //销售员ID
        sellerId:string    
        //销售姓名
        sellerName:string    
    }
}
