<script setup lang="ts">
import CalloutRecordTable from '../callout-record-table.vue';
import { computed } from 'vue';

const props = defineProps({
    customerMobile: {
        type: String,
        default: '',
    },
    visible: {
        type: Boolean,
        required: true,
        default: false,
    },
});

const emit = defineEmits(['update:visible']);

const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => {
        if (value === false) {
            emit('update:visible', false);
        }
    },
});
</script>

<template>
    <el-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="通话记录"
        width="80%"
        custom-class="callout-record-dialog"
    >
        <callout-record-table
            type="mobile"
            :mobile="customerMobile"
        />
    </el-dialog>
</template>
<style lang="scss">
.callout-record-dialog {
    .el-dialog__body {
        padding: 0;
    }
}
</style>
