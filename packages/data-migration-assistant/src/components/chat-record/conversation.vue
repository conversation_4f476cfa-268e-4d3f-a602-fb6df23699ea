<script setup lang="ts">
const props = defineProps({
    timeline: {
        type: Object,
        required: true,
    },
});

/**
 * 获取链接后缀
 * @param url 链接
 */
const getVoiceType = (url: string) => {
    if (!url) { return ''; }
    // 去除查询参数和 hash
    const urlWithoutQuery = url.split('?')[0].split('#')[0];
    // 提取文件扩展名
    const match = urlWithoutQuery.match(/\.([^.]+)$/);
    return match ? match[1].toLowerCase() : '';
};

/**
 * 图片点击事件
 */
const handleImageClick = () => {
    const imageView = document.querySelector('.el-image-viewer__wrapper__hidden.display-none');
    if (imageView) {
        imageView.className = 'el-image-viewer__wrapper';
    }
};

/**
 * 获取用户事件类型
 * @param event 事件数据
 */
const getEventMsg = (event: any) => {
    if (!event) {
        return '';
    }
    let eventMsg = '';
    switch (event.event_type) {
        // 用户进入会话事件
        case 'enter_session':
            eventMsg = '用户进入会话！！！';
            break;
        // 消息发送失败事件
        case 'msg_send_fail':
            eventMsg = '消息发送失败！！！';
            break;
        // 接待人员接待状态变更事件
        case 'servicer_status_change':
            eventMsg = '接待人员接待状态变更！！！';
            break;
        // 会话状态变更事件
        case 'session_status_change':
            eventMsg = '会话状态变更！！！';
            break;
        // 用户撤回消息事件
        case 'user_recall_msg':
            eventMsg = '用户撤回消息！！！';
            break;
        // 接待人员撤回消息事件
        case 'servicer_recall_msg':
            eventMsg = '接待人员撤回消息！！！';
            break;
        default:
            eventMsg = '';
    }
    return eventMsg;
};
</script>
<template>
    <span v-if="timeline.msgType === 'text'">{{ timeline.msgText?.content || '' }}</span>
    <el-image
        v-else-if="timeline.msgType === 'image'"
        :id="timeline.id"
        style="height: 200px; max-width: 200px;"
        :src="timeline.msgImage?.ossUrl || timeline.msgMedia?.oss_file_name"
        :preview-src-list="[timeline.msgImage?.ossUrl || timeline.msgMedia?.oss_file_name]"
        :initial-index="4"
        :close-on-press-escape="false"
        hide-on-click-modal
        fit="contain"
        alt="图片"
        @click="handleImageClick"
    >
        <template #placeholder>
            <div class="image-slot">
                <el-icon color="#ccc" size="24px"><Picture /></el-icon>
            </div>
        </template>
    </el-image>
    <div v-else-if="timeline.msgType === 'voice'">
        <audio
            v-if="getVoiceType(timeline.msgVoice?.ossUrl || timeline.msgMedia?.oss_file_name) !== 'amr'
                && (timeline.msgVoice?.ossUrl || timeline.msgMedia?.oss_file_name)"
            controls
            :src="timeline.msgVoice?.ossUrl || timeline.msgMedia?.oss_file_name"
        ></audio>
        <span v-else-if="getVoiceType(timeline.msgVoice?.ossUrl || timeline.msgMedia?.oss_file_name) !== 'amr'" class="conversation-voice-error">
            <el-icon><Mute /></el-icon>
            <span style="padding-left: 12px;">音频文件丢失...</span>
        </span>
        <div v-else>
            <a :href="timeline.msgVoice?.ossUrl || timeline.msgMedia?.oss_file_name || ''">点击下载音频</a>
            <span v-if="timeline.msgText?.content">（语音转文字内容：{{ timeline.msgText?.content }}）</span>
        </div>
    </div>
    <video
        v-else-if="timeline.msgType === 'video'"
        controls
        style="height: 400px;"
        :src="timeline.msgVideo?.ossUrl || timeline.msgMedia?.oss_file_name"
    ></video>
    <p v-else-if="timeline.msgType === 'revoke'">
        <span>你撤回了一条消息...</span>
    </p>
    <el-link
        v-else-if="timeline.msgType === 'file'"
        type="primary"
        :href="timeline.msgFile?.ossUrl || timeline.msgMedia?.oss_file_name"
        target="_blank"
    >
        {{ timeline.msgFile?.filename || '未命名文件' }}
    </el-link>
    <el-link
        v-else-if="timeline.msgType === 'link'"
        type="primary"
        target="_blank"
        :href="timeline.msgLink?.linkUrl || timeline.msgLink?.url"
    >
        {{ timeline.msgLink?.title || '链接地址' }}
    </el-link>
    <div v-else-if="timeline.msgType === 'location'">
        <p>地名：{{ timeline.msgLocation?.name || '' }}</p>
        <p>地址：{{ timeline.msgLocation?.address || '' }}</p>
        <p>{{ `经度：${timeline.msgLocation?.longitude || ''}   纬度：${ timeline.msgLocation?.latitude ||''}` }} </p>
    </div>
    <div v-else-if="timeline.msgType === 'card'">
        名片：
        <p>{{ timeline.msgCard?.corpname || '' }}</p>
        <span>{{ timeline.msgCard?.userid || '' }}</span>
    </div>
    <p v-else-if="timeline.msgType === 'business_card'">名片：{{ timeline.msgBusinessCard?.userid || '' }}</p>
    <div v-else-if="timeline.msgType === 'msgmenu'">
        <el-card>
            <template #header>
                <div class="card-header">
                    <span>{{ timeline.msgMenu?.head_content || '' }}</span>
                </div>
            </template>
            <template v-if="timeline.msgMenu?.list">
                <p v-for="msg in timeline.msgMenu.list" :key="msg.type">
                    <span v-if="msg.type === 'click'">{{ msg.click?.content || '' }}</span>
                    <a v-else-if="msg.type === 'view'" target="_blank" :href="msg.view?.url">{{ msg.view?.content || '' }}</a>
                    <a
                        v-else-if="msg.type === 'miniprogram'"
                        target="_blank"
                        :href="msg.miniprogram?.pagepath"
                    >{{ msg.miniprogram?.content || '' }}</a>
                </p>
            </template>
            <p v-if="timeline.msgMenu?.tail_content">{{ timeline.msgMenu.tail_content }}</p>
        </el-card>
    </div>
    <p v-else-if="timeline.msgType === 'event'">
        <span style="font-weight: bolder; font-size: 16px;">{{ getEventMsg(timeline.msgEvent) }}</span>
    </p>
    <div v-else-if="timeline.msgType === 'voiptext'" class="conversation-voiptext">
        <el-icon v-if="timeline.msgVoiptext?.invitetype % 2 === 0"><PhoneFilled /></el-icon>
        <el-icon v-else><VideoCamera /></el-icon>
        <span style="padding-left: 12px;">本次通话：{{ timeline.msgVoiptext?.callduration || '0' }}s</span>
    </div>
    <div v-else-if="timeline.msgType === 'mixed'">
        <div v-for="item in timeline.msgMixed.item" :key="item">
            <span v-if="item.type === 'text'">{{ item.content? JSON.parse(item.content).content : '' }}</span>
            <el-image
                v-else-if="item.type === 'image'"
                :id="item.id"
                style="height: 200px; max-width: 200px;"
                :src="item.content? JSON.parse(item.content).ossUrl || JSON.parse(item.content).oss_file_name : ''"
                :preview-src-list="[item.content? JSON.parse(item.content).ossUrl || JSON.parse(item.content).oss_file_name: '']"
                :initial-index="4"
                :close-on-press-escape="false"
                hide-on-click-modal
                fit="contain"
                alt="图片"
                @click="handleImageClick"
            >
                <template #placeholder>
                    <div class="image-slot">
                        <el-icon color="#ccc" size="24px"><Picture /></el-icon>
                    </div>
                </template>
            </el-image>
        </div>
    </div>
    <span v-else>其他消息类型暂未支持，请期待后续更新！</span>
</template>
<style lang="scss">
.conversation-voiptext,
.conversation-voice-error {
    width: 200px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px;
    border-radius: 4px;
    background-color: #f5f7fa;
}

.display-none {
    display: none;
}
</style>
