<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useFormat } from '@/composables/date';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    htmlValue: {
        type: String,
        default: '',
    },
    currentData: {
        type: Object,
        default: () => {
        },
    },
});
const emit = defineEmits(['update:visible']);
const dialogVisible = computed({
    get() {
        return props.visible;
    },
    set(v) {
        emit('update:visible', v);
    },
});
function handleDialogClose() {
    console.log('close');
}
const customWidth = ref('680px');
const customHeight = ref('720px');
const customClass = ref('');
const handleResolutionChange = (width: string, height: string) => {
    customWidth.value = width;
    customHeight.value = height;
    const screenWidth = parseInt(width);
    if (screenWidth > 480) {
        customClass.value = 'editor-preview-wrapper-pc';
    } else if (screenWidth > 390) {
        customClass.value = 'screen-480-767';
    } else if (screenWidth > 320) {
        customClass.value = 'screen-320-479';
    }
};
</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        title="富文本预览"
        :width="780"
        custom-class="editor-preview-dialog"
        @close="handleDialogClose"
    >
        <div>
            <el-button @click="handleResolutionChange('720px', '720px')">PC 680*720</el-button>
            <el-button @click="handleResolutionChange('390px', '844px')">iphone 12 pro 390*844</el-button>
            <el-button @click="handleResolutionChange('430px', '932px')">iphone 14 pro Max 430*932</el-button>
        </div>
        <div style="margin: 4px 0;">
            <el-button @click="handleResolutionChange('375px', '812px')">iphone X 375*812</el-button>
            <el-button @click="handleResolutionChange('360px', '780px')">Mobile 360*780</el-button>
            <el-button @click="handleResolutionChange('320px', '569px')">Mobile 320*569</el-button>
        </div>
        <div
            class="question-dialog-content-wrapper"
            :class="customClass"
            :style="{width: customWidth, height: customHeight}"
        >
            <div class="question-dialog-title-wrapper">
                <span class="title">
                    {{ currentData.title }}
                </span>
                <p class="text">
                    <i class="ri-time-line" style="font-size: 16px; color: #aab4bf; padding-right: 6px;"></i>
                    <span style="padding-right: 24px;">更新于 {{ useFormat(currentData.lastModified) }}</span>
                    <span>
                        <i class="ri-eye-line" style="font-size: 16px; color: #aab4bf; padding-right: 6px;"></i>
                        <span>{{ currentData.viewsCount }}</span>
                    </span>
                </p>
            </div>
            <div
                v-for="(v,i) in currentData.videos"
                :key="i"
            >
                <div v-if="v.url && v.qrCodeLink" class="video">
                    <video
                        height="360"
                        controls
                        :src="v.url"
                    ></video>
                    <div class="qr-code">
                        <div class="img-wrapper">
                            <img :src="v.qrCodeLink">
                        </div>
                        <span>扫码查看视频</span>
                    </div>
                </div>
            </div>
            <div
                class="editor-preview-wrapper"
                v-html="htmlValue"
            ></div>
        </div>
    </el-dialog>
</template>
<style lang="scss">
@import '../index.scss';

.editor-preview-dialog {
    .el-dialog__body {
        margin: 0 auto;
        max-height: 70vh;
    }

    .question-dialog-content-wrapper {
        padding: 40px;
        margin: 0 auto;
        overflow-y: auto;
        border: 1px solid var(--el-border-color);
        border-radius: 4px;

        .question-dialog-title-wrapper {
            display: flex;
            flex-direction: column;

            .title {
                font-size: 24px;
                line-height: 34px;
                font-weight: bold;
                color: #000;
                margin-bottom: 8px;
            }

            .text {
                color: #7a8794;
                line-height: 26px;
                font-size: 14px;
            }
        }

        .video {
            margin: 32px 0;
            position: relative;

            &:hover {
                .qr-code {
                    opacity: 1;
                }
            }

            > video {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .qr-code {
                position: absolute;
                top: 10px;
                right: 10px;
                padding: 12px 12px 8px 12px;
                background-color: #fff;
                border-radius: 4px;
                display: flex;
                flex-direction: column;
                align-items: center;
                opacity: 0;
                transition: all .2s;

                .img-wrapper {
                    width: 72px;
                    height: 72px;

                    > img {
                        width: 100%;
                        height: 100%;
                    }
                }

                > img {
                    margin-bottom: 8px;
                }

                > span {
                    color: #7a8794;
                    font-size: 12px;
                    line-height: 16px;
                }
            }
        }
    }

    .editor-preview-wrapper {
        margin: 0 auto;

        &.screen-480-767 {
            .abc-editor__custom-image {
                width: 85% !important;
            }
        }

        &.screen-320-479 {
            .abc-editor__custom-image {
                width: 100% !important;
            }
        }
    }
}
</style>
