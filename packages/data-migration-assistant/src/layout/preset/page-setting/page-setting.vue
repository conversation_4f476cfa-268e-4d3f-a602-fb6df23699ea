<script setup lang="ts">
import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
import LayoutPageSidebar from '@/layout/components/page-container/layout-page-sidebar.vue';
import SecondaryMenu from './secondary-menu.vue';
import { LayoutPageContainerPreset } from '@/layout/components/page-container/constants.ts';
import LayoutPageMain from '@/layout/components/page-container/layout-page-main.vue';
import { OaTabs } from '@abc-oa/components';
import { computed, ref, watch } from 'vue';
import useNavigator from '@/composables/navigator.ts';

function handleTabChange(value: string) {
    console.log('value', value);
    toMenu({
        name: value,
    });
}

const { tertiaryMenus, tertiaryActiveMenuItem, toMenu } = useNavigator();
const tabs = computed(() => tertiaryMenus.value?.map((item) => ({
    label: item.meta?.name,
    value: item.name,
})));

const currentTab = ref(tertiaryActiveMenuItem.value?.name);

watch(tertiaryActiveMenuItem, (newVal) => {
    if (newVal) {
        currentTab.value = newVal.name;
    } else {
        currentTab.value = '';
    }
});

</script>

<template>
    <layout-page-container :preset="LayoutPageContainerPreset.Setting">
        <layout-page-sidebar>
            <template #main>
                <secondary-menu></secondary-menu>
            </template>
        </layout-page-sidebar>
        <layout-page-main>
            <template v-if="tabs" #header>
                <oa-tabs
                    v-if="tabs"
                    v-model="currentTab"
                    :options="tabs"
                    @change="handleTabChange"
                >
                </oa-tabs>
            </template>
            <router-view></router-view>
        </layout-page-main>
    </layout-page-container>
</template>
