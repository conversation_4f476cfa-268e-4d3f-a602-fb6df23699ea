<script setup lang="ts">
import useNavigator from '@/composables/navigator.ts';

function handleMenuClick(menuItem: any) {
    toMenu(menuItem);
}

const { secondaryMenus, secondaryActiveMenuItem, toMenu } = useNavigator();
console.log('secondaryMenus', secondaryMenus, secondaryActiveMenuItem);
</script>

<template>
    <div class="oa-secondary-menu">
        <el-menu
            v-if="secondaryMenus"
            :default-active="secondaryActiveMenuItem?.name"
            @open="handleOpen"
            @close="handleClose"
        >
            <el-menu-item
                v-for="(menuItem, index) in secondaryMenus"
                :key="index"
                :index="menuItem.name"
                @click="handleMenuClick(menuItem)"
            >
                <oa-icon v-if="menuItem.meta?.icon" :icon="menuItem.meta?.icon"></oa-icon>
                <span>{{ menuItem.meta?.name }}</span>
            </el-menu-item>
        </el-menu>
    </div>
</template>

<style lang="scss">
.oa-secondary-menu {
    width: 100%;
    height: 100%;
    background: rgba(249, 250, 252, 1);
    padding: var(--oa-padding-8);

    .el-menu {
        .el-menu-item {
            --el-menu-text-color: var(--oa-text-color);
            --el-menu-active-color: #fff;
            --el-menu-hover-bg-color: #eaedf1;

            &.is-active {
                background-color: var(--oa-blue-2);
            }

            & + .el-menu-item {
                margin-top: var(--oa-padding-4);
            }
        }
    }
}
</style>
